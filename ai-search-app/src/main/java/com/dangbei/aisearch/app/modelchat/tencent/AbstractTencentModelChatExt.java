package com.dangbei.aisearch.app.modelchat.tencent;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.modelchat.AbstractModelChatExt;
import com.dangbei.aisearch.client.dto.clientobject.TencentChatResponse;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.common.enums.ProviderEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.infrastructure.config.properties.TencentCloudProperties;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.SSEResponseModel;
import com.tencentcloudapi.lkeap.v20240522.LkeapClient;
import com.tencentcloudapi.lkeap.v20240522.models.ChatCompletionsRequest;
import com.tencentcloudapi.lkeap.v20240522.models.ChatCompletionsResponse;
import com.tencentcloudapi.lkeap.v20240522.models.Message;
import com.theokanning.openai.Usage;
import com.theokanning.openai.completion.chat.AssistantMessage;
import com.theokanning.openai.completion.chat.ChatCompletionChoice;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 腾讯模型对话扩展点
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-25
 */
@Slf4j
public abstract class AbstractTencentModelChatExt extends AbstractModelChatExt {

    @Resource
    private TencentCloudProperties tencentCloudProperties;

    /**
     * 构建查询参数
     * @param ctx 上下文
     * @return 调用Param
     */
    protected abstract ChatCompletionRequest buildParam(ChatContext ctx);

    @Override
    public Flowable<ChatCompletionResult> chatCompletion(ChatContext ctx) throws Exception {

        ChatCompletionRequest openAiRequest = buildParam(ctx);

        // 填充模型信息
        ctx.setUsageInfo(new TokenUsageCo(ProviderEnum.TENCENT_CLOUD.getCode(), openAiRequest.getModel()));

        // RAG检索内容处理
        RagResponse ragResponse = ragSearch(openAiRequest, ctx);

        ChatCompletionsRequest request = convertRequest(openAiRequest);

        // 结构转换
        Credential cred = new Credential(tencentCloudProperties.getLkeapSecretId(), tencentCloudProperties.getLkeapSecretKey());
        ChatCompletionsResponse resp = new LkeapClient(cred, "ap-shanghai").ChatCompletions(request);
        return Flowable.fromIterable(resp)
            .doOnTerminate(resp::close)
            .map(res -> convertCompletionResult(res, request, ragResponse));
    }

    /**
     * 将Openai输入结构转为腾讯输入结构
     * @param request openAi输入结构
     * @return 腾讯输入结构
     */
    private ChatCompletionsRequest convertRequest(ChatCompletionRequest request) {
        Message[] messages = new Message[request.getMessages().size()];
        for (int i = 0; i < request.getMessages().size(); i++) {
            Message item = new Message();
            item.setRole(request.getMessages().get(i).getRole());
            item.setContent(request.getMessages().get(i).getTextContent());
            messages[i] = item;
        }


        // 转换request
        ChatCompletionsRequest req = new ChatCompletionsRequest();
        req.setModel(request.getModel());
        req.setStream(true);
        req.setMessages(messages);
        req.setTemperature(Convert.toFloat(request.getTemperature()));
        req.setMaxTokens(Convert.toLong(request.getMaxTokens()));

        log.info("调用腾讯ChatCompletionsRequest\n{}", ObjectMapperUtil.toJson(req));
        return req;
    }

    /**
     * 将腾讯CompletionResult转为Openai输出结构
     * @param tencentResult 腾讯流式Result
     * @param request       腾讯请求对象
     * @param ragResponse   RAG检索结果
     * @return Openai输出结构
     */
    @NotNull
    private static ChatCompletionResult convertCompletionResult(SSEResponseModel.SSE tencentResult,
                                                                ChatCompletionsRequest request,
                                                                RagResponse ragResponse) {
        if (Objects.isNull(tencentResult) || !JSONUtil.isTypeJSONObject(tencentResult.Data)) {
            return new ChatCompletionResult();
        }

        TencentChatResponse sseResp = JSON.parseObject(tencentResult.Data, TencentChatResponse.class);

        ChatCompletionResult target = new ChatCompletionResult();
        target.setId(sseResp.getId());

        // 使用量
        TencentChatResponse.ChatUsage tencentUsage = sseResp.getUsage();
        target.setUsage(convertUsage(tencentUsage));

        // 时间
        target.setCreated(sseResp.getCreated());

        // 模型
        target.setModel(request.getModel());

        // 服务提供商
        target.setLlmProvider(ProviderEnum.TENCENT_CLOUD.getCode());

        // RAG结果转换
        target.setRagSearchInfo(ragResponse);

        // 模型回答
        target.setChoices(convertChoices(sseResp.getChoices()));

        return target;
    }

    private static List<ChatCompletionChoice> convertChoices(TencentChatResponse.Choice[] tencentChoices) {
        if (Objects.nonNull(tencentChoices) && tencentChoices.length > 0) {
            List<ChatCompletionChoice> choices = new ArrayList<>(1);
            for (int i = 0; i < tencentChoices.length; i++) {
                TencentChatResponse.Choice choice = tencentChoices[i];
                var delta = choice.getDelta();
                AssistantMessage assMessage = new AssistantMessage();
                assMessage.setContent(delta.getContent());
                assMessage.setReasoningContent(delta.getReasoningContent());
                // TODO Took call

                ChatCompletionChoice targetChoice = new ChatCompletionChoice();
                targetChoice.setIndex(i);
                targetChoice.setFinishReason(choice.getFinishReason());
                targetChoice.setMessage(assMessage);
                choices.add(targetChoice);
            }
            return choices;
        }
        return null;
    }

    private static Usage convertUsage(TencentChatResponse.ChatUsage tencentUsage) {
        if (Objects.nonNull(tencentUsage)) {
            Usage targetUsage = new Usage();
            targetUsage.setPromptTokens(tencentUsage.getPromptTokens());
            targetUsage.setCompletionTokens(tencentUsage.getCompletionTokens());
            targetUsage.setTotalTokens(tencentUsage.getTotalTokens());
            return targetUsage;
        }
        return null;
    }

}
