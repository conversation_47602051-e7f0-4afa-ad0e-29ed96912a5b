package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.fastjson2.JSON;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.clientobject.ChatModelConfigCo;
import com.dangbei.aisearch.client.dto.clientobject.SettingConfigItemCo;
import com.dangbei.aisearch.client.enums.SettingPropEnum;
import com.dangbei.aisearch.domain.entity.SettingConfigEntity;
import com.dangbei.aisearch.domain.gateway.SettingConfigGateway;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 首页用户模型配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-24
 */
@Component
public class UserModelConfigQueryExe {

    @Resource
    private SettingConfigGateway settingConfigGateway;
    @Resource
    private ChatModelConfigQueryExe chatModelConfigQueryExe;

    public MultiResponse<ChatModelConfigCo.ModelList> execute() {
        ChatModelConfigCo chatModelConfigCo = chatModelConfigQueryExe.execute().getData();
        // 顺序调整
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        SettingConfigEntity configEntity = settingConfigGateway.getConfig(userDeviceInfo.getUserId(), userDeviceInfo.getDeviceId());
        if (Objects.nonNull(configEntity)) {
            sort(chatModelConfigCo.getModelList(), configEntity.getConfig());
        }

        // 取前5个
        return MultiResponse.of(chatModelConfigCo.getModelList().stream().limit(5).collect(Collectors.toList()));
    }

    private static void sort(List<ChatModelConfigCo.ModelList> allModel, List<SettingConfigItemCo> userConfigs) {
        if (CollUtil.isEmpty(userConfigs)) {
            return;
        }
        SettingConfigItemCo recentModel = userConfigs.stream().filter(i -> StrUtil.equals(i.getPropKey(), SettingPropEnum.recentModels.getPropKey())).findFirst().orElse(null);
        if (Objects.isNull(recentModel)) {
            return;
        }
        List<String> recentModels = JSON.parseArray(Convert.toStr(recentModel.getPropVal()), String.class);
        if (CollUtil.isEmpty(recentModels)) {
            return;
        }

        // 对modelList进行排序，优先ModelList.isRecommend=true，紧接着按recentModels排序（ModelList.getValue），剩余的按原始顺序
        Map<String, Integer> recentModelOrder = Maps.newHashMap();
        for (int i = 0; i < recentModels.size(); i++) {
            recentModelOrder.put(recentModels.get(i), i);
        }

        allModel.sort((a, b) -> {
            // Step 1: recommend priority
            if (a.isRecommend() && !b.isRecommend()) {
                return -1;
            }
            if (!a.isRecommend() && b.isRecommend()) {
                return 1;
            }

            // Step 2: recentModels order
            Integer aIndex = recentModelOrder.get(a.getValue());
            Integer bIndex = recentModelOrder.get(b.getValue());
            if (aIndex != null && bIndex != null) {
                return aIndex.compareTo(bIndex);
            } else if (aIndex != null) {
                return -1;
            } else if (bIndex != null) {
                return 1;
            }

            // Step 3: original order preserved - by not modifying order if both values are not in recentModels
            return 0;
        });
    }

    public static void main(String[] args) {
        // 测试一下排序
        List<ChatModelConfigCo.ModelList> modelList = new ArrayList<>();

        modelList.add(new ChatModelConfigCo.ModelList().setValue("model-1").setRecommend(false));
        modelList.add(new ChatModelConfigCo.ModelList().setValue("model-2").setRecommend(false));
        modelList.add(new ChatModelConfigCo.ModelList().setValue("model-3").setRecommend(true));
        modelList.add(new ChatModelConfigCo.ModelList().setValue("model-4").setRecommend(false));
        modelList.add(new ChatModelConfigCo.ModelList().setValue("model-5").setRecommend(false));

        // 模拟 recentModels
        List<String> recentModels = Arrays.asList("model-4", "model-6");

        // 构造 SettingConfigEntity
        SettingConfigItemCo item = new SettingConfigItemCo();
        item.setPropKey(SettingPropEnum.recentModels.getPropKey());
        item.setPropVal(JSON.toJSONString(recentModels));

        System.out.println("排序前：");
        modelList.forEach(m -> System.out.println(m.getValue() + " (recommend: " + m.isRecommend() + ")"));

        sort(modelList, Collections.singletonList(item));

        System.out.println("\n排序后：");
        modelList.forEach(m -> System.out.println(m.getValue() + " (recommend: " + m.isRecommend() + ")"));
    }

}
