package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.common.enums.YesOrNo;
import com.alibaba.cola.common.util.BeanCopyUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aidoggyscreenshot.client.dto.cmd.query.MessagePageQuery;
import com.dangbei.aisearch.app.assembler.ShareAssembler;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.clientobject.MessageListCo;
import com.dangbei.aisearch.client.dto.clientobject.ShareListCo;
import com.dangbei.aisearch.client.dto.cmd.query.SharePageQuery;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import com.dangbei.aisearch.common.enums.ShareSourceEnum;
import com.dangbei.aisearch.common.enums.ShareStatusEnum;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.CommunityPostEntity;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.entity.ShareEntity;
import com.dangbei.aisearch.domain.gateway.CommunityPostGateway;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import com.dangbei.aisearch.domain.gateway.ExternalScreenshotGateway;
import com.dangbei.aisearch.domain.gateway.ShareGateway;
import com.dangbei.aisearch.infrastructure.convertor.ChatMessageConvertor;
import com.dangbei.aisearch.infrastructure.convertor.QuickChatMessageConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ChatMessageDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.QuickChatMessageDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ChatMessageMapper;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.QuickChatMessageMapper;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-06
 */
@Component
public class SharePageQueryExe {

    @Resource
    private ShareGateway shareGateway;
    @Resource
    private ConversationGateway conversationGateway;
    @Resource
    private ChatMessageMapper chatMessageMapper;
    @Resource
    private ChatMessageConvertor chatMessageConvertor;
    @Resource
    private ShareAssembler shareAssembler;
    @Resource
    private ExternalScreenshotGateway externalScreenshotGateway;
    @Resource
    private CommunityPostGateway communityPostGateway;
    @Resource
    private QuickChatMessageMapper quickChatMessageMapper;
    @Resource
    private QuickChatMessageConvertor quickChatMessageConvertor;
    @Resource
    private MessageListQueryExe messageListQueryExe;

    public SingleResponse<ShareListCo> pageQuery(SharePageQuery pageQuery) {
        String shareId = pageQuery.getShareId();
        ShareEntity shareEntity = shareGateway.getByShareId(shareId);
        Assert.notNull(shareEntity, "分享不存在");

        ShareListCo shareListCo = null;
        if (ShareStatusEnum.isNormal(shareEntity.getShareStatus())) {
            if (shareEntity.isSourceScreenshot()) {
                shareListCo = getScreenshotShareListCo(pageQuery, shareEntity);
            } else if (shareEntity.isQuick()) {
                shareListCo = shareAssembler.toShareListCo(getQuickMessageListCo(pageQuery, shareEntity));
            } else {
                shareListCo = shareAssembler.toShareListCo(getMessageListCo(pageQuery, shareEntity));
            }
        }

        boolean expired = Objects.nonNull(shareEntity.getExpireTime()) && LocalDateTime.now().isAfter(shareEntity.getExpireTime());

        // 广场帖子状态查询
        if (StringUtils.startsWith(shareId, ShareSourceEnum.COMMUNITY_POST.getPrefix())) {
            CommunityPostEntity postEntity = communityPostGateway.getByShareId(shareId);
            if (Objects.isNull(postEntity)) {
                expired = true;
            }
        }

        UserDeviceDTO userDeviceDTO = UserDeviceUtil.getUserDeviceInfo();
        if (!ShareStatusEnum.isNormal(shareEntity.getShareStatus())
            || Objects.isNull(shareListCo)
            || expired) {
            shareListCo = new ShareListCo();
            shareListCo.setHasMore(false);
            shareListCo.setMsgList(Lists.newArrayList());
            shareListCo.setIsOwn(false);
        }
        shareListCo.setConversationId(shareEntity.getConversationId());
        shareListCo.setShareStatus(shareEntity.getShareStatus());
        shareListCo.setCreateTime(shareEntity.getCreateTime());
        shareListCo.setLikeNum(shareEntity.getLikeNum());
        shareListCo.setExpireTime(shareEntity.getExpireTime());
        shareListCo.setExpired(expired);

        shareListCo.setIsOwn(UserDeviceUtil.isOwn(shareEntity.getUserId(), shareEntity.getDeviceId()));
        return SingleResponse.of(shareListCo);
    }

    private ShareListCo getScreenshotShareListCo(SharePageQuery pageQuery, ShareEntity shareEntity) {
        ShareListCo shareListCo;
        MessagePageQuery query = BeanCopyUtil.copyOf(pageQuery, MessagePageQuery::new);
        query.setConversationId(shareEntity.getConversationId());
        shareListCo = shareAssembler.toShareListCo(externalScreenshotGateway.messagePageQuery(query));
        if (Objects.nonNull(shareListCo) && StringUtils.isBlank(shareListCo.getConversationTitle())) {
            shareListCo.setConversationTitle(I18nUtil.get(I18nValueEnum.CHAT_DEFAULT_CONVERSATION_TITLE));
        }
        return shareListCo;
    }

    private MessageListCo getMessageListCo(SharePageQuery pageQuery, ShareEntity shareEntity) {
        ConversationEntity conversationEntity = conversationGateway.getByConversationId(shareEntity.getConversationId());
        Assert.notNull(conversationEntity, "会话不存在");

        Page<ChatMessageDO> page = Page.of(pageQuery.getPageIndex(), pageQuery.getPageSize());
        LambdaQueryWrapper<ChatMessageDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ChatMessageDO::getConversationId, shareEntity.getConversationId());
        queryWrapper.in(ChatMessageDO::getMsgId, shareEntity.getMsgIds());
        queryWrapper.orderByDesc(ChatMessageDO::getId);
        Page<ChatMessageDO> messagePage = chatMessageMapper.selectPage(page, queryWrapper);

        List<ChatMessageEntity> entityList = chatMessageConvertor.toEntityList(messagePage.getRecords());
        return messageListQueryExe.getMessageListCo(entityList, messagePage.getTotal(), pageQuery.getPageSize(), conversationEntity.getTitle(), conversationEntity.getTitleSummaryFlag());
    }

    private MessageListCo getQuickMessageListCo(SharePageQuery pageQuery, ShareEntity shareEntity) {
        Page<QuickChatMessageDO> page = Page.of(pageQuery.getPageIndex(), pageQuery.getPageSize());
        LambdaQueryWrapper<QuickChatMessageDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(QuickChatMessageDO::getConversationId, shareEntity.getConversationId());
        queryWrapper.orderByDesc(QuickChatMessageDO::getId);
        Page<QuickChatMessageDO> messagePage = quickChatMessageMapper.selectPage(page, queryWrapper);
        return messageListQueryExe.getMessageListCo(quickChatMessageConvertor.toChatMessageEntityList(messagePage.getRecords()), messagePage.getTotal(), pageQuery.getPageSize(), I18nUtil.get(I18nValueEnum.CHAT_DEFAULT_CONVERSATION_TITLE), YesOrNo.YES.getValue());
    }

}
