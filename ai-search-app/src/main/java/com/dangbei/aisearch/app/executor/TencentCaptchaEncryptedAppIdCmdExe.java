package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.client.dto.cmd.TencentCaptchaEncryptedAppIdCmd;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.devbase.client.dto.clientobject.TencentEncryptedCaptchaAppIdCo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 腾讯云验证码获取加密AppId执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-15
 */
@Slf4j
@Component
public class TencentCaptchaEncryptedAppIdCmdExe {

    @Resource
    private ExternalCommonGateway externalCommonGateway;

    public SingleResponse<String> execute(TencentCaptchaEncryptedAppIdCmd cmd) {
        TencentEncryptedCaptchaAppIdCo resp = externalCommonGateway.getTencentCaptchaEncryptedAppId(cmd.getCaptchaAppId());
        Assert.notNull(resp, "获取加密AppId失败");
        return SingleResponse.of(resp.getAidEncrypted());
    }
}
