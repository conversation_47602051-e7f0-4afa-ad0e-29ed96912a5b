package com.dangbei.aisearch.app.assembler;

import cn.hutool.core.util.StrUtil;
import com.dangbei.aisearch.client.dto.clientobject.ConversationCo;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ConversationDO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-08
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ConversationAssembler {

    ConversationCo toCo(ConversationEntity conversation);

    List<ConversationCo> toCoList(List<ConversationDO> conversationList);

    @AfterMapping
    default void removeLineBreaks(@MappingTarget ConversationCo co) {
        if (co != null && co.getTitle() != null) {
            co.setTitle(StrUtil.removeAllLineBreaks(co.getTitle()));
        }
    }

}
