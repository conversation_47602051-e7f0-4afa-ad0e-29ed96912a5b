package com.dangbei.aisearch.app.assembler;

import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.enums.YesOrNo;
import com.alibaba.fastjson2.JSON;
import com.coze.openapi.client.connversations.message.model.MessageContentType;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.dangbei.aisearch.app.util.RequestThreadLocalUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.ChatMsgExt;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-11
 */
@Component
public class ChatMessageAssembler {

    @Resource
    private ExternalCommonGateway externalCommonGateway;

    public ChatMessageEntity toAssistantMessageEntity(ChatCmd cmd, String msgId, String answerContent, String chatId) {
        ChatMessageEntity entity = new ChatMessageEntity();
        entity.setRole(RoleEnum.ASSISTANT.getRoleType());
        entity.setType(MessageType.ANSWER.getValue());
        entity.setConversationId(cmd.getConversationId());
        entity.setBotCode(cmd.getBotCode());
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        entity.setUserId(userDeviceInfo.getUserId());
        entity.setDeviceId(userDeviceInfo.getDeviceId());
        entity.setStream(YesOrNo.getByBoolean(cmd.getStream()).getValue());
        entity.setQuestion(cmd.getQuestion());
        entity.setFiles(CollectionUtils.isNotEmpty(cmd.getFiles()) ? ObjectMapperUtil.toJson(cmd.getFiles()) : null);
        entity.setMsgId(msgId);
        entity.setContent(answerContent);
        entity.setContentType(MsgContentTypeEnum.TEXT.getValue());
        entity.setChatId(chatId);
        long currTimeStamp = System.currentTimeMillis() / 1000;
        entity.setCreatedAt(currTimeStamp);
        entity.setUpdatedAt(currTimeStamp);

        // 扩展信息
        ChatMsgExt chatMsgExt = new ChatMsgExt();
        chatMsgExt.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        entity.setExt(chatMsgExt);
        entity.setAppType(RequestThreadLocalUtil.getAppType());
        return entity;
    }

    public ChatMessageEntity toUserMessageEntity(ChatCmd cmd, String msgId, String chatId) {
        ChatMessageEntity entity = new ChatMessageEntity();
        entity.setRole(RoleEnum.USER.getRoleType());
        entity.setType(MessageType.QUESTION.getValue());
        entity.setConversationId(cmd.getConversationId());
        entity.setBotCode(cmd.getBotCode());
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        entity.setUserId(userDeviceInfo.getUserId());
        entity.setDeviceId(userDeviceInfo.getDeviceId());
        entity.setStream(YesOrNo.getByBoolean(cmd.getStream()).getValue());
        entity.setQuestion(cmd.getQuestion());
        entity.setFiles(CollectionUtils.isNotEmpty(cmd.getFiles()) ? ObjectMapperUtil.toJson(cmd.getFiles()) : null);
        entity.setMsgId(msgId);
        entity.setContent(cmd.getQuestion());
        entity.setContentType(MessageContentType.TEXT.getValue());
        entity.setChatId(chatId);
        long currTimeStamp = System.currentTimeMillis() / 1000;
        entity.setCreatedAt(currTimeStamp);
        entity.setUpdatedAt(currTimeStamp);

        ChatMsgExt chatMsgExt = new ChatMsgExt();
        chatMsgExt.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        chatMsgExt.setChatCmd(JSON.toJSONString(cmd));
        if (CollectionUtils.isNotEmpty(cmd.getReference())) {
            chatMsgExt.setReferences(ObjectMapperUtil.toJson(cmd.getReference()));
        }
        entity.setExt(chatMsgExt);
        entity.setAppType(RequestThreadLocalUtil.getAppType());
        return entity;
    }

    public List<ChatMessageEntity> getQuestionAndAnswerMessageList(ChatCmd chatCmd, String answerContent) {
        List<ChatMessageEntity> messageList = new ArrayList<>(2);
        String chatId = externalCommonGateway.getDistributedId();
        String questionMsgId = externalCommonGateway.getDistributedId();
        String answerMsgId = externalCommonGateway.getDistributedId();
        // 提问Entity
        ChatMessageEntity question = toUserMessageEntity(chatCmd, questionMsgId, chatId);
        messageList.add(question);
        // 回答Entity
        ChatMessageEntity answer = toAssistantMessageEntity(chatCmd, answerMsgId, answerContent, chatId);
        messageList.add(answer);
        return messageList;
    }
}
