package com.dangbei.aisearch.app.agentchat;

import cn.hutool.core.collection.CollUtil;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.modelchat.dashscope.AbstractDashScopeModelChatExt;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.domain.entity.AgentEntity;
import com.dangbei.aisearch.domain.gateway.AgentGateway;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.SystemMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;

/**
 * 智能体聊天
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-28
 */
@Component
public class AgentChat extends AbstractDashScopeModelChatExt {

    @Resource
    private AgentGateway agentGateway;

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        AgentEntity agentEntity = agentGateway.getByAgentId(ctx.getAgentId());

        // 上下文记忆
        LinkedList<ChatMessage> messages = new LinkedList<>();
        messages.addFirst(new SystemMessage(agentEntity.getSystemPrompt()));
        messages.addAll(handleMemory(filterMemoryTurns(ctx.getHistory(), 100)));
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        return ChatCompletionRequest.builder()
            .model("qwen-long")
            .messages(messages)
            .build();
    }

    public List<ChatMessage> handleMemory(List<ChatMessage> chatMessages) {
        if (CollUtil.isEmpty(chatMessages)) {
            return chatMessages;
        }

        // 首个元素必须是User
        Iterator<ChatMessage> iterator = chatMessages.iterator();
        while (iterator.hasNext()) {
            ChatMessage message = iterator.next();
            if (!RoleEnum.USER.eq(message.getRole())) {
                iterator.remove();
            } else {
                break;
            }
        }

        // 最后一个元素必须是Assistant
        ListIterator<ChatMessage> listIterator = chatMessages.listIterator(chatMessages.size());
        while (listIterator.hasPrevious()) {
            ChatMessage message = listIterator.previous();
            if (!RoleEnum.ASSISTANT.eq(message.getRole())) {
                listIterator.remove();
            } else {
                break;
            }
        }

        return chatMessages;
    }

}
