//package com.dangbei.aisearch.app.extension;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.convert.Convert;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.cola.extension.Extension;
//import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
//import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationOutput;
//import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
//import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
//import com.alibaba.dashscope.common.MultiModalMessage;
//import com.alibaba.dashscope.common.Role;
//import com.alibaba.fastjson.JSON;
//import com.coze.openapi.client.connversations.message.model.MessageType;
//import com.dangbei.aisearch.app.model.MessageModel;
//import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
//import com.dangbei.aisearch.common.constant.CommonConst;
//import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
//import com.dangbei.aisearch.common.enums.RoleEnum;
//import com.dangbei.aisearch.common.util.ObjectMapperUtil;
//import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
//import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
//import io.reactivex.Flowable;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.MDC;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//import static com.alibaba.cola.common.constant.RequestConstant.REQUEST_ID;
//
///**
// * 图像理解对话扩展点
// * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2025-01-15
// */
//@Slf4j
//@Extension(bizId = CommonConst.Intent.IMAGE_UNDERSTAND)
//public class ImageUnderstandChatExt extends AbstractChatExt {
//
//    @Resource
//    private DashScopeProperties dashScopeProperties;
//
//    @Override
//    public void chatCompletion(ChatContext ctx) throws Exception {
//        // TODO 线程池
//        MultiModalConversation conv = new MultiModalConversation();
//        Flowable<MultiModalConversationResult> result = conv.streamCall(buildParam(ctx.getHistory(), ctx.getChatCmd()));
//
//        result.blockingForEach(data -> {
//            MultiModalConversationOutput.Choice choice = data.getOutput().getChoices().get(0);
//
//            // 增量回答
//            String deltaMsg = Convert.toStr(choice.getMessage().getContent().get(0).get("text"));
//            MessageModel.Msg delta = new MessageModel.Msg()
//                .setId(ctx.getAnswerMsgId())
//                .setType(MessageType.ANSWER.getValue())
//                .setRole(choice.getMessage().getRole())
//                .setContent(deltaMsg)
//                .setContentType(MsgContentTypeEnum.TEXT.getValue())
//                .setConversationId(ctx.getConversationId())
//                .setRequestId(MDC.get(REQUEST_ID))
//                .setParentMsgId(ctx.getQuestionMsgId());
//            ctx.sendSSE("conversation.message.delta", ObjectMapperUtil.toJson(delta));
//            ctx.appendAnswer(deltaMsg);
//        });
//    }
//
//    private MultiModalConversationParam buildParam(LinkedList<ChatMessageEntity> history, ChatCmd cmd) {
//        // 历史对话填充，三轮
//        LinkedList<MultiModalMessage> messages = new LinkedList<>();
//        messages.addFirst(MultiModalMessage.builder()
//            .role(Role.SYSTEM.getValue())
//            .content(Collections.singletonList(Collections.singletonMap("text", "你是当贝研发的AI助手.")))
//            .build());
//        messages.addAll(getRecentThreeTurns(history));
//        // 用户当前提问
//        List<Map<String, Object>> maps = new ArrayList<>();
//        if (CollUtil.isNotEmpty(cmd.getFiles())) {
//            maps = cmd.getFiles().stream()
//                .filter(i -> "image".equals(i.getType()) && StrUtil.isNotBlank(i.getFileUrl()))
//                .map(i -> new HashMap<String, Object>() {{
//                    put("image", i.getFileUrl());
//                }})
//                .collect(Collectors.toList());
//        }
//        maps.add(new HashMap<String, Object>() {{
//            put("text", cmd.getQuestion());
//        }});
//        MultiModalMessage userMessage = MultiModalMessage.builder()
//            .role(Role.USER.getValue())
//            .content(maps)
//            .build();
//        messages.add(userMessage);
//        log.info("历史上下文：{}", JSON.toJSONString(messages));
//
//        return MultiModalConversationParam.builder()
//            .apiKey(dashScopeProperties.getApiKey())
//            .model(dashScopeProperties.getMultiModalConfig().getModel())
//            .messages(messages)
//            .incrementalOutput(true)
//            .enableSearch(true)
//            .build();
//    }
//
//    public static List<MultiModalMessage> getRecentThreeTurns(List<ChatMessageEntity> history) {
//        if (CollUtil.isEmpty(history)) {
//            return new ArrayList<>();
//        }
//
//        LinkedList<MultiModalMessage> recentMessages = new LinkedList<>();
//        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息
//        for (ChatMessageEntity entity : history) {
//            if (RoleEnum.USER.eq(entity.getRole())) {
//                List<Map<String, Object>> maps = new ArrayList<>();
//                if (StrUtil.isNotBlank(entity.getFiles())) {
//                    maps = JSON.parseArray(entity.getFiles(), ChatCmd.FileItem.class).stream()
//                        .filter(i -> "image".equals(i.getType()) && StrUtil.isNotBlank(i.getFileUrl()))
//                        .map(i -> new HashMap<String, Object>() {{
//                            put("image", i.getFileUrl());
//                        }})
//                        .collect(Collectors.toList());
//                }
//                maps.add(new HashMap<String, Object>() {{
//                    put("text", entity.getContent());
//                }});
//
//                MultiModalMessage userMessage = MultiModalMessage.builder()
//                    .role(Role.USER.getValue())
//                    .content(maps).build();
//                recentMessages.addFirst(userMessage);
//            }
//            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
//                MultiModalMessage robotMessage = MultiModalMessage.builder()
//                    .role(Role.ASSISTANT.getValue())
//                    .content(Collections.singletonList(
//                        new HashMap<String, Object>() {{
//                            put("text", entity.getContent());
//                        }}
//                    )).build();
//                recentMessages.addFirst(robotMessage);
//            }
//            if (recentMessages.size() >= 12) {
//                break; // 找到三轮对话，退出
//            }
//        }
//        return recentMessages;
//    }
//
//}
