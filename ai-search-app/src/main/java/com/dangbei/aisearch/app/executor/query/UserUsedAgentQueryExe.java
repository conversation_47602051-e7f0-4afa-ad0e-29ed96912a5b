package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.clientobject.MyAgentCo;
import com.dangbei.aisearch.client.dto.cmd.AgentConversationDeleteCmd;
import com.dangbei.aisearch.client.dto.cmd.RemoveMyAgentCmd;
import com.dangbei.aisearch.client.enums.AgentOnlineStatusEnum;
import com.dangbei.aisearch.client.enums.AgentVisibilityEnum;
import com.dangbei.aisearch.domain.entity.AgentConversationEntity;
import com.dangbei.aisearch.domain.entity.RecommendAgentEntity;
import com.dangbei.aisearch.domain.entity.UserAgentEntity;
import com.dangbei.aisearch.domain.gateway.AgentConversationGateway;
import com.dangbei.aisearch.domain.gateway.AgentGateway;
import com.dangbei.aisearch.domain.gateway.RecommendAgentGateway;
import com.dangbei.aisearch.domain.gateway.UserAgentGateway;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentConversationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentConversationMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025-03-13 21:05
 **/
@Component
public class UserUsedAgentQueryExe {

    @Resource
    private AgentGateway agentGateway;
    @Resource
    private UserAgentGateway userAgentGateway;
    @Resource
    private AgentConversationGateway agentConversationGateway;
    @Resource
    private AgentConversationMapper agentConversationMapper;
    @Resource
    private RecommendAgentGateway recommendAgentGateway;
    @Resource
    private AgentConversationDeleteCmdExe agentConversationDeleteCmdExe;
    @Value("${agent.myAgent.show-num:5}")
    private Integer myAgentShowNum;

    public List<MyAgentCo> getMyAgent() {
        List<MyAgentCo> resultList = new ArrayList<>();
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        // ①优先取出用户聊过的智能体
        List<AgentConversationEntity> agentConversationList = agentConversationGateway.listByUserIdOrDeviceId(userDeviceInfo, myAgentShowNum * 3);
        if (CollUtil.isNotEmpty(agentConversationList)) {
            var agentMap = agentGateway.getAgentMap(agentConversationList.stream()
                .map(AgentConversationEntity::getAgentId)
                .toList());
            resultList = agentConversationList.stream()
                .map(source -> {
                    var targetAgent = agentMap.get(source.getAgentId());
                    // 过滤掉非正常状态的智能体
                    if (!AgentOnlineStatusEnum.isNormal(targetAgent.getOnlineStatus())) {
                        return null;
                    }
                    // 是私密状态且不是创建者
                    if (AgentVisibilityEnum.isPrivate(targetAgent.getVisibility()) && !Objects.equals(UserContextUtil.getUserId(), targetAgent.getCreateUserId())) {
                        return null;
                    }
                    MyAgentCo myAgentCo = new MyAgentCo();
                    myAgentCo.setAgentId(source.getAgentId());
                    myAgentCo.setConversationId(source.getConversationId());
                    myAgentCo.setName(targetAgent.getName());
                    myAgentCo.setIntro(targetAgent.getIntro());
                    // myAgentCo.setDescription(targetAgent.getDescription());
                    myAgentCo.setIconUrl(targetAgent.getIconUrl());
                    // myAgentCo.setFollowUp(targetAgent.getFollowUp());
                    myAgentCo.setCreateShowPerson(targetAgent.getCreateShowPerson());
                    myAgentCo.setHasAppBgUrl(StrUtil.isNotBlank(targetAgent.getAppBgUrl()));
                    myAgentCo.setAppBgUrl(targetAgent.getAppBgUrl());
                    myAgentCo.setAppBgColorTone(targetAgent.getAppBgColorTone());
                    myAgentCo.setRecommend(false);
                    myAgentCo.setVoiceType(targetAgent.getVoiceType());
                    return myAgentCo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        }

        // ②数量足够，直接返回
        if (resultList.size() >= myAgentShowNum) {
            return resultList.subList(0, myAgentShowNum);
        }

        // ③数量不够，取推荐数据进行补位
        List<String> deletedAgentIds = syncAndGetExcludeAgentIds(userDeviceInfo);
        deletedAgentIds.addAll(agentConversationList.stream().map(AgentConversationEntity::getAgentId).toList());

        var needNum = myAgentShowNum - resultList.size();
        var recommendAgentList = recommendAgentGateway.list(deletedAgentIds, needNum);
        if (CollectionUtils.isNotEmpty(recommendAgentList)) {
            var agentMap = agentGateway.getAgentMap(recommendAgentList.stream().map(RecommendAgentEntity::getAgentId).toList());
            resultList.addAll(
                recommendAgentList.stream()
                    .map(source -> {
                        var targetAgent = agentMap.get(source.getAgentId());
                        // 过滤掉非正常状态的智能体
                        if (!AgentOnlineStatusEnum.isNormal(targetAgent.getOnlineStatus())) {
                            return null;
                        }
                        MyAgentCo myAgentCo = new MyAgentCo();
                        myAgentCo.setAgentId(source.getAgentId());
                        myAgentCo.setConversationId(null);
                        myAgentCo.setName(targetAgent.getName());
                        myAgentCo.setIntro(targetAgent.getIntro());
                        // myAgentCo.setDescription(targetAgent.getDescription());
                        myAgentCo.setIconUrl(targetAgent.getIconUrl());
                        // myAgentCo.setFollowUp(targetAgent.getFollowUp());
                        myAgentCo.setCreateShowPerson(targetAgent.getCreateShowPerson());
                        myAgentCo.setHasAppBgUrl(StrUtil.isNotBlank(targetAgent.getAppBgUrl()));
                        myAgentCo.setAppBgUrl(targetAgent.getAppBgUrl());
                        myAgentCo.setAppBgColorTone(targetAgent.getAppBgColorTone());
                        myAgentCo.setRecommend(true);
                        myAgentCo.setVoiceType(targetAgent.getVoiceType());
                        return myAgentCo;
                    })
                    .filter(Objects::nonNull)
                    .toList()
            );
        }
        return resultList;
    }

    /**
     * 同步并获取已删除的智能体记录
     * @return 已删除的智能体ids
     */
    private List<String> syncAndGetExcludeAgentIds(UserDeviceDTO userDeviceInfo) {
        UserAgentEntity userAgentEntity = userAgentGateway.getByUserDevice(userDeviceInfo);
        if (Objects.isNull(userAgentEntity)) {
            return new ArrayList<>();
        }
        if (StrUtil.isBlank(userAgentEntity.getDeprecatedAgents())) {
            return new ArrayList<>();
        }

        // ①用户删除过的智能体
        List<String> deletedAgents = JSON.parseArray(userAgentEntity.getDeprecatedAgents(), String.class);
        return Objects.isNull(deletedAgents) ? new ArrayList<>() : deletedAgents;
    }

    /**
     * 获取用户在使用的智能体（聊过且生效的）
     * @param userDeviceInfo 用户登录信息
     * @return 在使用的智能体ID
     */
    private List<String> getInUseAgents(UserDeviceDTO userDeviceInfo) {
        if (StrUtil.isAllBlank(userDeviceInfo.getUserId(), userDeviceInfo.getDeviceId())) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AgentConversationDO> wrapper = Wrappers.lambdaQuery();
        wrapper.select(AgentConversationDO::getAgentId);
        if (StrUtil.isNotBlank(userDeviceInfo.getUserId())) {
            wrapper.eq(AgentConversationDO::getUserId, userDeviceInfo.getUserId());
        } else if (StrUtil.isNotBlank(userDeviceInfo.getDeviceId())) {
            wrapper.eq(AgentConversationDO::getDeviceId, userDeviceInfo.getDeviceId());
        }
        return agentConversationMapper.selectList(wrapper).stream()
            .map(AgentConversationDO::getAgentId)
            .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public Response remove(RemoveMyAgentCmd cmd) {
        // ①无脑删一下会话
        AgentConversationDeleteCmd deleteCmd = new AgentConversationDeleteCmd();
        deleteCmd.setAgentId(cmd.getAgentId());
        agentConversationDeleteCmdExe.execute(deleteCmd);

        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        // ②写入or更新弃用记录表
        UserAgentEntity entity = ObjectUtil.defaultIfNull(userAgentGateway.getByUserDevice(userDeviceInfo), new UserAgentEntity());
        entity.setUserId(userDeviceInfo.getUserId());
        entity.setDeviceId(userDeviceInfo.getDeviceId());
        entity.addDeprecatedAgentId(cmd.getAgentId());
        entity.saveOrUpdate();

        return Response.buildSuccess();
    }

}
