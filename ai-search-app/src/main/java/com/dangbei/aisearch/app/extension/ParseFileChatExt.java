//package com.dangbei.aisearch.app.extension;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.cola.extension.Extension;
//import com.alibaba.dashscope.aigc.generation.GenerationOutput;
//import com.alibaba.dashscope.aigc.generation.GenerationParam;
//import com.alibaba.dashscope.aigc.generation.GenerationResult;
//import com.alibaba.dashscope.common.Message;
//import com.alibaba.dashscope.common.Role;
//import com.alibaba.fastjson.JSON;
//import com.coze.openapi.client.connversations.message.model.MessageType;
//import com.dangbei.aisearch.infrastructure.factory.AutoCloseGeneration;
//import com.dangbei.aisearch.infrastructure.factory.PoolDashScopeObjectFactory;
//import com.dangbei.aisearch.app.model.MessageModel;
//import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
//import com.dangbei.aisearch.common.constant.CommonConst;
//import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
//import com.dangbei.aisearch.common.enums.RoleEnum;
//import com.dangbei.aisearch.common.util.ObjectMapperUtil;
//import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
//import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
//import io.reactivex.Flowable;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.MDC;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//import static com.alibaba.cola.common.constant.RequestConstant.REQUEST_ID;
//
///**
// * 图像理解对话扩展点
// * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2025-01-15
// */
//@Slf4j
//@Extension(bizId = CommonConst.Intent.PARSE_FILE)
//public class ParseFileChatExt extends AbstractChatExt {
//    @Resource
//    private DashScopeProperties dashScopeProperties;
//
//    @Override
//    @SneakyThrows
//    protected void sendLoading(ChatContext ctx) {
//        MessageModel.Msg searchMsg = buildProgress(ctx.getConversationId(), "解析文件中");
//        ctx.sendSSE("conversation.message.delta", ObjectMapperUtil.toJson(searchMsg));
//    }
//
//    @Override
//    public void chatCompletion(ChatContext ctx) throws Exception {
//        try (AutoCloseGeneration gen = PoolDashScopeObjectFactory.getGeneration()) {
//            Flowable<GenerationResult> result = gen.streamCall(buildParam(ctx.getHistory(), ctx.getChatCmd()));
//
//            result.blockingForEach(data -> {
//                GenerationOutput.Choice choice = data.getOutput().getChoices().get(0);
//                log.info("大模型增量输出：{}", JSON.toJSONString(data));
//
//                // ③增量回答
//                MessageModel.Msg delta = new MessageModel.Msg()
//                    .setId(ctx.getAnswerMsgId())
//                    .setType(MessageType.ANSWER.getValue())
//                    .setRole(choice.getMessage().getRole())
//                    .setContent(choice.getMessage().getContent())
//                    .setContentType(MsgContentTypeEnum.TEXT.getValue())
//                    .setConversationId(ctx.getConversationId())
//                    .setRequestId(MDC.get(REQUEST_ID))
//                    .setParentMsgId(ctx.getQuestionMsgId());
//                ctx.sendSSE("conversation.message.delta", ObjectMapperUtil.toJson(delta));
//                ctx.appendAnswer(choice.getMessage().getContent());
//            });
//        }
//    }
//
//    private MessageModel.Msg buildProgress(String conversationId, String content) {
//        return new MessageModel.Msg()
//            .setConversationId(conversationId)
//            .setRole(RoleEnum.ASSISTANT.getRoleType())
//            .setType(MessageType.ANSWER.getValue())
//            .setContentType(MsgContentTypeEnum.PROGRESS.getValue())
//            .setContent(content)
//            .setCreatedAt(DateUtil.currentSeconds())
//            .setRequestId(MDC.get(REQUEST_ID));
//    }
//
//    private GenerationParam buildParam(List<ChatMessageEntity> history, ChatCmd cmd) {
//        // 历史对话填充，六轮
//        LinkedList<Message> messages = new LinkedList<>();
//        messages.addFirst(Message.builder().role(Role.SYSTEM.getValue()).content(dashScopeProperties.getFileParseModel().getSystem()).build());
//        messages.addAll(getRecentThreeTurns(history, 6));
//        // TODO 文件ID
//        if (CollUtil.isNotEmpty(cmd.getFiles())) {
//            List<Message> fileSysMsg = cmd.getFiles().stream()
//                .filter(i -> "file".equals(i.getType()) && StrUtil.isNotBlank(i.getFileId()))
//                .map(i -> Message.builder().role(Role.SYSTEM.getValue()).content("fileid://" + i.getFileId()).build())
//                .collect(Collectors.toList());
//            messages.addAll(fileSysMsg);
//        }
//        // TODO 引用拼接
//        String referenceText = CollUtil.isNotEmpty(cmd.getReference()) ? cmd.getReference().stream().filter(i -> "text".equals(i.getType())).map(ChatCmd.ReferenceItem::getText).findFirst().orElse(null) : null;
//        String question = StrUtil.isNotBlank(referenceText) ? StrUtil.format("用户选中文字: {} \n 用户要求：{}", referenceText, cmd.getQuestion()) : cmd.getQuestion();
//        messages.add(Message.builder().role(Role.USER.getValue()).content(question).build());
//        log.info("历史上下文：{}", JSON.toJSONString(messages));
//
//        return GenerationParam.builder()
//            .apiKey(dashScopeProperties.getApiKey())
//            .model(dashScopeProperties.getFileParseModel().getModel())
//            .messages(messages)
//            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
//            .incrementalOutput(true)
//            .build();
//    }
//
//    private List<Message> getRecentThreeTurns(List<ChatMessageEntity> history, Integer turns) {
//        if (CollUtil.isEmpty(history)) {
//            return new ArrayList<>();
//        }
//
//        LinkedList<Message> recentMessages = new LinkedList<>();
//        // 遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
//        for (ChatMessageEntity entity : history) {
//            if (RoleEnum.USER.eq(entity.getRole())) {
//                // 用户消息
//                recentMessages.addFirst(Message.builder().role(Role.USER.getValue()).content(entity.getContent()).build());
//                // 文件消息
//                if (StrUtil.isNotBlank(entity.getExt().getChatCmd())) {
//                    ChatCmd chatCmd = JSON.parseObject(entity.getExt().getChatCmd(), ChatCmd.class);
//                    if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
//                        chatCmd.getFiles().stream()
//                            .filter(i -> "file".equals(i.getType()) && StrUtil.startWith(i.getFileId(), "file-fe"))
//                            .map(i -> Message.builder().role(Role.SYSTEM.getValue()).content("fileid://" + i.getFileId()).build())
//                            .forEach(recentMessages::addFirst);
//                    }
//                }
//            }
//            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
//                recentMessages.addFirst(Message.builder().role(Role.ASSISTANT.getValue()).content(entity.getContent()).build());
//            }
//            if (recentMessages.size() >= turns * 2) {
//                break; // 找到三轮对话，退出
//            }
//        }
//        return recentMessages;
//    }
//
//}
