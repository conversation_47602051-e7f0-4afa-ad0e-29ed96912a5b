package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.clientobject.AgentConversationCo;
import com.dangbei.aisearch.client.dto.cmd.query.AgentConversationDetailQuery;
import com.dangbei.aisearch.domain.entity.AgentEntity;
import com.dangbei.aisearch.domain.gateway.AgentGateway;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentConversationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.AgentConversationMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 智能体会话详情查询
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-25
 */
@Component
public class AgentConversationDetailQueryExe {

    @Resource
    private AgentGateway agentGateway;
    @Resource
    private AgentConversationMapper agentConversationMapper;

    public SingleResponse<AgentConversationCo> execute(AgentConversationDetailQuery query) {
        UserDeviceDTO userDeviceInfo = UserDeviceUtil.getUserDeviceInfo();
        LambdaQueryWrapper<AgentConversationDO> queryWrapper = Wrappers.lambdaQuery(AgentConversationDO.class);
        if (StringUtils.isNotBlank(userDeviceInfo.getUserId())) {
            queryWrapper.eq(AgentConversationDO::getUserId, userDeviceInfo.getUserId());
        } else if (StringUtils.isNotBlank(userDeviceInfo.getDeviceId())) {
            queryWrapper.eq(AgentConversationDO::getDeviceId, userDeviceInfo.getDeviceId());
        }
        queryWrapper.eq(AgentConversationDO::getConversationId, query.getConversationId());
        List<AgentConversationDO> list = agentConversationMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return SingleResponse.of(null);
        }

        AgentConversationDO conversation = list.get(0);
        AgentEntity agentEntity = agentGateway.getByAgentId(conversation.getAgentId());
        if (Objects.isNull(agentEntity)) {
            return SingleResponse.of(null);
        }

        AgentConversationCo target = new AgentConversationCo();
        target.setConversationId(conversation.getConversationId());
        target.setAgentId(agentEntity.getAgentId());
        target.setName(agentEntity.getName());
        target.setIntro(agentEntity.getIntro());
        target.setIconUrl(agentEntity.getIconUrl());
        target.setCreateShowPerson(agentEntity.getCreateShowPerson());
        target.setHasAppBgUrl(StrUtil.isNotBlank(agentEntity.getAppBgUrl()));
        target.setAppBgUrl(agentEntity.getAppBgUrl());
        target.setDescription(agentEntity.getDescription());
        String createUserId = agentEntity.getCreateUserId();
        if (StringUtils.isNotBlank(createUserId) && Objects.equals(createUserId, UserContextUtil.getUserId())) {
            target.setCreatorIsSelf(true);
        }
        return SingleResponse.of(target);
    }

}
