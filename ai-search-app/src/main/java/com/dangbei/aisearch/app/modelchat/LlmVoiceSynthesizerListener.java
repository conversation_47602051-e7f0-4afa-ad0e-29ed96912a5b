package com.dangbei.aisearch.app.modelchat;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.dto.DTO;
import com.alibaba.cola.dto.Response;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.infrastructure.tts.TTSSynthesizerListener;
import com.dangbei.aisearch.infrastructure.tts.protocol.TTSResponse;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.dangbei.platform.wsserver.client.dto.cmd.PushBinaryMessageCmd;
import com.dangbei.platform.wsserver.client.dto.cmd.PushMessageCmd;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.Map;
import java.util.Objects;

/**
 * 大模型回答TTS合成监听器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Setter
@Getter
@Slf4j
public class LlmVoiceSynthesizerListener extends TTSSynthesizerListener {

    private String requestId;
    private String wsId;
    private String sessionId;
    private String msgId;
    private String wsAppKey;
    private Map<String, Object> ext = Maps.newHashMap();
    private ExternalCommonGateway externalCommonGateway;

    public LlmVoiceSynthesizerListener() {
    }

    public LlmVoiceSynthesizerListener(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public void onClose(int closeCode, String reason) {
        super.onClose(closeCode, reason);

        SessionDataCo sessionDataCo = new SessionDataCo()
            .setSessionId(sessionId)
            .setMsgId(msgId);

        PushMessageCmd cmd = new PushMessageCmd();
        cmd.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        cmd.setAppKey(wsAppKey);
        cmd.setToWsId(wsId);
        cmd.setData(WsMsgData.of(WsMsgType.ttsSessionFinished, sessionDataCo).toMap());
        Response sendResponse = getExternalCommonGateway().pushMessage(cmd);

        MDC.put(RequestConstant.REQUEST_ID, requestId);
        log.info("===>onSessionFinished sendResponse:{}", sendResponse);
    }

    @Override
    protected void onSessionStarted(TTSResponse response) {
        SessionDataCo sessionDataCo = new SessionDataCo()
            .setSessionId(sessionId)
            .setMsgId(msgId);

        PushMessageCmd cmd = new PushMessageCmd();
        cmd.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
        cmd.setAppKey(wsAppKey);
        cmd.setToWsId(wsId);
        cmd.setData(WsMsgData.of(WsMsgType.ttsSessionStarted, sessionDataCo).toMap());
        Response sendResponse = getExternalCommonGateway().pushMessage(cmd);

        MDC.put(RequestConstant.REQUEST_ID, requestId);
        log.info("===>onSessionStarted sendResponse:{}", sendResponse);
    }

    @Override
    protected void onAudioResponse(byte[] bytes) {
        MDC.put(RequestConstant.REQUEST_ID, requestId);
        // 判断redis中当前的wsId关联的最新session是否与当前一致，不一致则不发送
        Object cacheSessionId = RedisUtil.get(String.format(CacheKey.WS_SESSION_ID, wsId));
        if (Objects.isNull(cacheSessionId)) {
            super.synthesizer.markClosed();
            super.synthesizer.close();

            return;
        }
        if (!StrUtil.equals(Convert.toStr(cacheSessionId), sessionId)) {
            super.synthesizer.markClosed();
            super.synthesizer.close();
            return;
        }

        PushBinaryMessageCmd cmd = new PushBinaryMessageCmd();
        cmd.setRequestId(wsId);
        cmd.setAppKey(wsAppKey);
        cmd.setToWsId(wsId);
        cmd.setData(bytes);
        cmd.setExt(ext);
        Response sendResponse = getExternalCommonGateway().pushBinaryMessage(cmd);
        log.debug("===>WS sendResponse:{}", sendResponse);

        if (!sendResponse.isSuccess()) {
            MDC.put(RequestConstant.REQUEST_ID, requestId);
            log.info("===>WS sendResponse wsId:{} errMessage:{}", wsId, sendResponse.getErrMessage());
            super.synthesizer.markClosed();
            super.synthesizer.close();
        }
    }

    private ExternalCommonGateway getExternalCommonGateway() {
        if (Objects.isNull(externalCommonGateway)) {
            return SpringUtil.getBean(ExternalCommonGateway.class);
        }
        return externalCommonGateway;
    }

    /**
     * 添加扩展参数
     * @param key 参数Key
     * @param val 参数Val
     */
    public void addExt(String key, Object val) {
        if (ext == null) {
            ext = Maps.newHashMap();
        }
        ext.put(key, val);
    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class WsMsgData<T> extends DTO {
        private boolean success = true;
        private String type;
        private T data;

        public static <T> WsMsgData<T> of(WsMsgType type, T data) {
            WsMsgData<T> response = new WsMsgData<>();
            response.setSuccess(true);
            response.setType(type.getType());
            response.setData(data);
            return response;
        }

        public static <T> WsMsgData<T> of(WsMsgType type) {
            WsMsgData<T> response = new WsMsgData<>();
            response.setSuccess(true);
            response.setType(type.getType());
            return response;
        }

        public Map<String, Object> toMap() {
            Map<String, Object> map = Maps.newHashMap();
            map.put("success", success);
            map.put("type", type);
            map.put("data", data);
            return map;
        }
    }

    @Getter
    @RequiredArgsConstructor
    public enum WsMsgType {
        ttsSessionStarted("ttsSessionStarted"),
        ttsSessionFinished("ttsSessionFinished");
        private final String type;
    }

    @Data
    @Accessors(chain = true)
    public static class SessionDataCo extends DTO {
        @Schema(description = "tts会话ID")
        private String sessionId;
        @Schema(description = "消息ID")
        private String msgId;
    }

}
