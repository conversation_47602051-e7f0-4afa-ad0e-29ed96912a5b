package com.dangbei.aisearch.app.modelchat.dashscope;

import com.alibaba.cola.extension.Extension;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.LinkedList;

/**
 * R1
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-24
 */
@Slf4j
@Component("r1_dashscope_model_chat")
@Extension(bizId = CommonConst.ModelIntent.DEEPSEEK_THINK)
public class R1 extends AbstractDashScopeModelChatExt {

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        // 上下文记忆
        LinkedList<ChatMessage> messages = filterMemoryTurns(ctx.getHistory(), 6);
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        return ChatCompletionRequest.builder()
            .model("deepseek-r1-distill-qwen-32b")
            .messages(messages)
            .build();
    }

}
