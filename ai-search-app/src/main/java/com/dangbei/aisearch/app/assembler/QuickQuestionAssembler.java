package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.QuickQuestionCo;
import com.dangbei.aisearch.client.dto.clientobject.QuickQuestionPageCo;
import com.dangbei.aisearch.domain.entity.QuickQuestionEntity;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.QuickQuestionDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 快捷提问数据转换器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-01
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface QuickQuestionAssembler {

    /**
     * DO转换为CO
     * @param questionDO 数据对象
     * @return 客户端对象
     */
    QuickQuestionCo toCo(QuickQuestionDO questionDO);

    /**
     * Entity转换为CO
     * @param questionEntity 实体对象
     * @return 列表客户端对象
     */
    QuickQuestionCo toCo(QuickQuestionEntity questionEntity);

    /**
     * DO列表转换为分页CO
     * @param questionDO 数据对象
     * @return 分页客户端对象
     */
    QuickQuestionPageCo toPageCo(QuickQuestionDO questionDO);
}
