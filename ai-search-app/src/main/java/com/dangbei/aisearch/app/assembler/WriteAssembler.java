package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.WriteCategoryCo;
import com.dangbei.aisearch.client.dto.clientobject.WriteCo;
import com.dangbei.aisearch.client.dto.clientobject.WriteMetaCo;
import com.dangbei.aisearch.domain.entity.WriteMetaEntity;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteCategoryDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.WriteMetaDO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface WriteAssembler {

    List<WriteCo> toWriteCoList(List<WriteDO> writeDOList);

    List<WriteMetaCo> toWriteMetaCoList(List<WriteMetaDO> writeMetaDOList);

    List<WriteCategoryCo> toWriteCategoryCoList(List<WriteCategoryDO> writeCategoryDOList);

    WriteMetaCo toWriteMetaCo(WriteMetaEntity metaEntity);

    @AfterMapping
    default void setIdAsValue(@MappingTarget WriteMetaCo metaCo) {
        metaCo.setValue(String.valueOf(metaCo.getId()));
    }
}
