package com.dangbei.aisearch.app.agentapprove.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-28 15:05
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AgentVersionApproveCo extends DTO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @Schema(description = "当前版本的智能体信息")
    private AgentApproveCo agentInfo;

    @Schema(description = "审核状态：0-审核中 1-审核通过 2-机器审核拒绝 3-人工审核拒绝 4-审核中止")
    private Integer approveStatus;

    @Schema(description = "审核拒绝原因")
    private String rejectReason;
}
