package com.dangbei.aisearch.app.modelchat.volc;

import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.modelchat.AbstractModelChatExt;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.common.enums.ProviderEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 豆包模型对话扩展点
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-22
 */
@Slf4j
public abstract class AbstractVolcModelChatExt extends AbstractModelChatExt {

    @Resource
    private ArkService arkService;

    /**
     * 构建查询参数
     * @param ctx 上下文
     * @return 调用Param
     */
    protected abstract ChatCompletionRequest buildParam(ChatContext ctx);

    @Override
    public Flowable<ChatCompletionResult> chatCompletion(ChatContext ctx) throws Exception {

        ChatCompletionRequest openAiRequest = buildParam(ctx);

        // 填充模型信息
        ctx.setUsageInfo(new TokenUsageCo(ProviderEnum.VOLCENGINE.getCode(), openAiRequest.getModel()));

        // RAG检索处理
        RagResponse ragResponse = ragSearch(openAiRequest, ctx);

        com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest request = convertRequest(openAiRequest);

        // 结构转换
        return arkService.streamChatCompletion(request).map(res -> convertCompletionResult(res, request, ragResponse));
    }

    /**
     * 将Openai输入结构转为火山输入结构
     * @param request openAi输入结构
     * @return 火山输入结构
     */
    private com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest convertRequest(ChatCompletionRequest request) {
        // 转换request
        com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest volRequest = ObjectMapperUtil.fromJson(ObjectMapperUtil.toJson(request), com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest.class);
        volRequest.setStreamOptions(com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest.ChatCompletionRequestStreamOptions.of(true));

        log.info("调用火山volRequest\n{}", ObjectMapperUtil.toJson(volRequest));
        return volRequest;
    }

    /**
     * 将百炼CompletionResult转为Openai输出结构
     * @param volResult   火山流式Result
     * @param request     火山请求对象
     * @param ragResponse RAG搜索结果
     * @return Openai输出结构
     */
    @NotNull
    private static ChatCompletionResult convertCompletionResult(ChatCompletionChunk volResult,
                                                                com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest request,
                                                                RagResponse ragResponse) {
        if (Objects.isNull(volResult)) {
            return new ChatCompletionResult();
        }

        ChatCompletionResult target = ObjectMapperUtil.fromJson(ObjectMapperUtil.toJson(volResult), ChatCompletionResult.class);

        // 服务提供商
        target.setLlmProvider(ProviderEnum.VOLCENGINE.getCode());

        // 联网结果转换
        target.setRagSearchInfo(ragResponse);

        return target;
    }

}
