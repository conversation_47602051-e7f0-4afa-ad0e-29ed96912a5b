package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.app.agentapprove.dto.AgentApproveCo;
import com.dangbei.aisearch.client.dto.clientobject.AgentCo;
import com.dangbei.aisearch.client.dto.clientobject.AgentListCo;
import com.dangbei.aisearch.client.dto.clientobject.AgentShareCo;
import com.dangbei.aisearch.client.dto.clientobject.AgentVersionCo;
import com.dangbei.aisearch.client.dto.clientobject.MyAgentPageCo;
import com.dangbei.aisearch.domain.entity.AgentEntity;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 * @date 2025-02-28 15:35
 **/
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AgentAssembler {

    AgentCo toCo(AgentDO agentDO);

    AgentListCo toListCo(AgentDO agentDO);

    AgentShareCo toShareCo(AgentEntity agentEntity);
    AgentApproveCo toApproveCo(AgentEntity agentEntity);

    @Mapping(target = "agentRole", source = "role")
    @Mapping(target = "agentName", source = "name")
    @Mapping(target = "agentAvatar", source = "iconUrl")
    AgentVersionCo toVersionCo(AgentEntity agentEntity);

    MyAgentPageCo toMyAgentPageCo(AgentDO agentDO);
}
