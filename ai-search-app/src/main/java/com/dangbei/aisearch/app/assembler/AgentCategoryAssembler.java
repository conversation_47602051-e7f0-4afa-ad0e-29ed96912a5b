package com.dangbei.aisearch.app.assembler;

import com.dangbei.aisearch.client.dto.clientobject.AgentPageCo;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.AgentCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-28 16:17
 **/
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface AgentCategoryAssembler {

    AgentPageCo.Category toCo(AgentCategoryDO agentCategoryDO);

    List<AgentPageCo.Category> toCos(List<AgentCategoryDO> agentCategoryDOS);
}
