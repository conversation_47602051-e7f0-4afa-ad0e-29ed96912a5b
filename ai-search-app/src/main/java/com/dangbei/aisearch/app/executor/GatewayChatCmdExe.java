package com.dangbei.aisearch.app.executor;

import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.constant.RequestConstant;
import com.alibaba.cola.common.util.HttpUtil;
import com.alibaba.cola.common.util.NanoIdUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.dashscope.exception.ApiException;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.app.model.MessageModel;
import com.dangbei.aisearch.app.modelchat.LlmVoiceSynthesizerListener;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.clientobject.SplitSearchKeywordResult;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.client.dto.cmd.RecentModelUpdateCmd;
import com.dangbei.aisearch.client.dto.cmd.query.MessageListQuery;
import com.dangbei.aisearch.client.enums.KnowledgeTypeEnum;
import com.dangbei.aisearch.client.enums.TtsSourceEnum;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.constant.ModelConst;
import com.dangbei.aisearch.common.enums.BotCodeEnum;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.common.util.SafeExecuteUtil;
import com.dangbei.aisearch.domain.entity.BaseDocEntity;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.ConversationAttachmentEntity;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import com.dangbei.aisearch.domain.gateway.ConversationAttachmentGateway;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.attachment.AttachmentResponse;
import com.dangbei.aisearch.infrastructure.common.helper.DocAccessHelper;
import com.dangbei.aisearch.infrastructure.common.helper.DocConfigHelper;
import com.dangbei.aisearch.infrastructure.config.properties.AppConfigProperties;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.config.properties.KnowledgeDocProperties;
import com.dangbei.aisearch.infrastructure.config.properties.ModelRouterProperties;
import com.dangbei.aisearch.infrastructure.config.properties.MonitorProperties;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeRequest;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeResponse;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeSearchApi;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.dangbei.aisearch.infrastructure.search.SearchRequest;
import com.dangbei.aisearch.infrastructure.search.SearchResponse;
import com.dangbei.aisearch.infrastructure.search.WebSearchApi;
import com.dangbei.aisearch.infrastructure.tts.TTSClient;
import com.dangbei.aisearch.infrastructure.tts.TTSSynthesizer;
import com.dangbei.aisearch.infrastructure.tts.protocol.OutputFormatEnum;
import com.dangbei.aisearch.infrastructure.tts.protocol.Protocol;
import com.dangbei.aisearch.infrastructure.tts.protocol.SampleRateEnum;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import com.dangbei.framework.insight.volcopenai.exception.ArkHttpException;
import com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatCompletionChunk;
import com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatCompletionRequest;
import com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessage;
import com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatMessageRole;
import com.dangbei.framework.insight.volcopenai.service.ArkService;
import com.google.common.collect.Maps;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dangbei.aisearch.common.constant.ModelConst.ThinkingModel.isReasoningModel;

/**
 * AI网关聊天接口命令执行器
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-15
 */
@Slf4j
@Component
public class GatewayChatCmdExe implements ModelConst {

    @Resource
    private TTSClient ttsClient;
    @Resource
    protected PromptUtil promptUtil;
    @Resource
    protected WebSearchApi webSearchApi;
    @Resource(name = "chatAsyncExecutor")
    protected Executor asyncExecutor;
    @Resource
    protected ArkService openAiArkService;
    @Resource
    private MonitorProperties monitorProperties;
    @Resource
    private KnowledgeSearchApi knowledgeSearchApi;
    @Resource
    protected ChatMessageGateway chatMessageGateway;
    @Resource
    protected DashScopeProperties dashScopeProperties;
    @Resource
    protected ConversationGateway conversationGateway;
    @Resource
    protected UserKnowledgeGateway userKnowledgeGateway;
    @Resource
    private KnowledgeDocProperties knowledgeDocProperties;
    @Resource
    protected ExternalCommonGateway externalCommonGateway;
    @Resource
    private UserKnowledgeDocGateway userKnowledgeDocGateway;
    @Resource
    private SharedKnowledgeDocGateway sharedKnowledgeDocGateway;
    @Resource(name = "ttftAsyncExecutor")
    protected ScheduledThreadPoolExecutor ttftAsyncExecutor;
    @Resource
    protected SplitSearchKeywordCmdExe splitSearchKeywordCmdExe;
    @Resource
    private ModelRouterProperties modelRouterProperties;
    @Resource
    private AppConfigProperties appConfigProperties;
    @Resource
    private MessageTtsCmdExe messageTtsCmdExe;
    @Resource
    private ConversationAttachmentGateway attachmentGateway;
    @Resource
    private VisualComprehensionCmdExe visualComprehensionCmdExe;
    @Resource
    private DocConfigHelper docConfigHelper;
    @Resource
    private DocAccessHelper docAccessHelper;
    @Resource
    private RecentModelUpdateCmdExe recentModelUpdateCmdExe;

    public Object execute(ChatCmd cmd, HttpServletRequest request) {
        // 0.前置校验
        ConversationEntity conversation = conversationGateway.getByConversationId(cmd.getConversationId());
        preCheck(cmd, conversation);

        // 1.异步更新上次聊天时间
        SafeExecuteUtil.execute(() -> syncTimeAndRecentModel(cmd.getModel(), cmd.getConversationId()));

        // 2.拽出当前会话的历史聊天记录
        LinkedList<ChatMessageEntity> history = listHistory(cmd.getConversationId());

        // 3.创建SseEmitter
        SseEmitter emitter = createEmitter();

        ChatContext chatCtx = new ChatContext(emitter, cmd, history, HttpUtil.getIp(request));
        chatCtx.setQuestionMsgId(genDistributedId());
        chatCtx.setAnswerMsgId(genDistributedId());
        chatCtx.setSupportDownload(Objects.nonNull(conversation) && conversation.isWriteScene());

        // 4.核心聊天方法！！！
        CompletableFuture.runAsync(() -> streamChat(chatCtx, buildChatCompletionRequest(chatCtx)), asyncExecutor);

        return emitter;
    }

    private void syncTimeAndRecentModel(String model, String conversationId) {
        // 更新会话时间
        conversationGateway.updateLastChatTime(conversationId);
        // 更新最近使用的模型
        recentModelUpdateCmdExe.execute(new RecentModelUpdateCmd().setModel(model));
    }

    /**
     * 流式对话
     * @param context 聊天上下文
     */
    protected void streamChat(ChatContext context, ChatCompletionRequest request) {
        try {
            log.info("调用网关Request\n{}", ObjectMapperUtil.toJson(request));

            // 定义延迟任务
            AtomicReference<ScheduledFuture<?>> firstTokenCostTaskRef = new AtomicReference<>();

            long startAt = System.currentTimeMillis();
            log.info("模型调用开始开始，时间戳：{}", startAt);
            Flowable<ChatCompletionChunk> flowable = openAiArkService.streamChatCompletion(request);
            flowable.doOnError(Throwable::printStackTrace)
                .doOnSubscribe(subscription -> flowableDoOnSubscribe(context, firstTokenCostTaskRef))
                .doFinally(() -> flowableDoFinally(firstTokenCostTaskRef))
                .blockingForEach(resp -> {

                    ChatMessage message = CollUtil.isNotEmpty(resp.getChoices()) ? resp.getChoices().get(0).getMessage() : null;

                    // 记录首token耗时
                    if (isFirstToken(message, context)) {
                        context.recordFirstTokenCost(startAt);
                    }

                    // 思考过程
                    if (isOutputThinking(message)) {
                        assert message != null;
                        context.appendThink(message.getReasoningContent());
                        context.sendDeltaThinkSSE(message.getReasoningContent());
                    }

                    // 模型回答
                    if (isOutputAnswer(message)) {
                        assert message != null;
                        context.appendAnswer(message.getContent().toString());
                        context.sendDeltaAnswerSSE(message.getContent().toString());
                        // 语音合成
                        sendTTSText(context.getTtsSynthesizer(), Convert.toStr(message.getContent()));
                    }

                    // Token消耗记录
                    context.collectUsageV2(resp, startAt);

                    // 记录请求链路ID
                    context.setRequestIdExt(resp.getId());

                });

            // 推荐问题
            if (StrUtil.isNotBlank(context.getMessageModel().getFullMsg()) && context.isEnableSuggest()) {
                context.sendFollowUpLoadingSSE();
                List<String> suggests = context.generateFollowUp();
                context.sendFollowUpSSE(suggests);
                context.addFollowUp(suggests);
            }

            // 发送结束标志
            context.sendChatCompletedSSE();
            context.getEmitter().complete();

        } catch (Exception e) {
            handleException(e, context);
        } finally {
            // 保存聊天记录
            SafeExecuteUtil.execute(context::asyncStoreChatMsg);
            // 保存附件的 conversationId
            SafeExecuteUtil.execute(context::asyncStoreAttachmentConversationId);
            // 关闭TTS session
            stopAndDestroy(context.getTtsSynthesizer());
        }
    }

    protected ChatCompletionRequest buildChatCompletionRequest(ChatContext context) {
        // 构建请求对象
        String model = modelRouterProperties.transferModel(context.getChatCmd().getModel(), context.getChatCmd().getUserAction());
        log.info("模型转换结果：{}", model);
        Assert.notBlank(model, "模型调用失败!");

        // 上下文记忆
        LinkedList<ChatMessage> memory = buildMemory(model, context);

        // 联网+知识库结果
        RagResponse ragResponse = ragSearch(context);

        // 改写用户提示词
        String rewriteMsg = rewriteUserMsg(context, ragResponse);

        // 组装用户提示词
        memory.add(ChatMessage.builder().role(ChatMessageRole.USER).content(rewriteMsg).build());

        ChatCompletionRequest request = ChatCompletionRequest.builder()
            .model(model)
            .stream(Boolean.TRUE)
            .messages(memory)
            .streamOptions(ChatCompletionRequest.ChatCompletionRequestStreamOptions.of(true, true))
            .build();
        return request;
    }

    /**
     * 初始化TTS合成器
     * @param ttsClient tts客户端
     * @param listener  tts合成监听器
     * @param voice     音色
     * @return TTSSynthesizer
     */
    public static TTSSynthesizer initTTSSynthesizer(TTSClient ttsClient,
                                                    LlmVoiceSynthesizerListener listener,
                                                    String voice) {
        try {
            // 初始化合成器
            TTSSynthesizer synthesizer = new TTSSynthesizer(ttsClient, listener);
            synthesizer.setUserId(UserDeviceUtil.getUserIdDefaultDeviceId());
            synthesizer.setSessionId(listener.getSessionId());
            synthesizer.setFormat(OutputFormatEnum.PCM);
            synthesizer.setVoice(voice);
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_24K);

            // redis中存储当前wsId关联的最新session
            RedisUtil.set(String.format(CacheKey.WS_SESSION_ID, listener.getWsId()), listener.getSessionId(), 60 * 10);

            return synthesizer;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 发送TTS文本给火山
     * @param synthesizer TTS合成器
     * @param content     内容
     */
    public static void sendTTSText(TTSSynthesizer synthesizer, String content) {
        if (StrUtil.isBlank(content) || Objects.isNull(synthesizer)) {
            return;
        }

        try {
            // 如果是第一次发送文本，先启动session
            if (Protocol.State.STATE_CONNECTED.equals(synthesizer.getState())) {
                synthesizer.startSession();
            }

            if (Protocol.State.STATE_REQUEST_CONFIRMED.equals(synthesizer.getState())) {
                synthesizer.send(content);
            }
        } catch (Exception ex) {
            log.error("sendTTS.error={}", ex.getMessage(), ex);
            // 若发送文本出现异常的话，切断连接，剩余的音频都不再接收
            try {
                synthesizer.close();
            } catch (Exception ignore) {
            }
        }
    }

    /**
     * 关闭TTS会话
     * @param synthesizer TTS合成器
     */
    public static void stopAndDestroy(TTSSynthesizer synthesizer) {
        if (Objects.isNull(synthesizer)) {
            return;
        }

        synthesizer.stopAndDestroy();
    }

    /**
     * 订阅时执行的操作
     * @param context 聊天上下文
     * @param taskRef 定时任务引用
     */
    private void flowableDoOnSubscribe(ChatContext context, AtomicReference<ScheduledFuture<?>> taskRef) {
        try {
            //  开关打开 # 在订阅时启动定时任务
            if (monitorProperties.isTtftWarnEnabled()) {
                ScheduledFuture<?> task = ttftAsyncExecutor.schedule(() -> {
                    log.info("首token检查时间：" + System.currentTimeMillis());

                    TokenUsageCo usageInfo = context.getUsageInfo();
                    if (Objects.isNull(usageInfo) || Objects.isNull(usageInfo.getFirstTokenCostMs())) {
                        log.error("""
                                首Token返回超时，当前阈值{}ms，请关注！
                                供应商：{}
                                模型：{}
                                """,
                            monitorProperties.getTtftMaxMs(),
                            Objects.nonNull(usageInfo) ? usageInfo.getProvider() : null,
                            Objects.nonNull(usageInfo) ? usageInfo.getModel() : null
                        );
                    }
                }, monitorProperties.getTtftMaxMs(), TimeUnit.MILLISECONDS);
                // 存储定时任务
                taskRef.set(task);
            }

            // ④初始化语音TTS websocket连接
            ChatCmd.ChatOption option = context.getChatCmd().getChatOption();
            if (Objects.nonNull(option) && StrUtil.isNotBlank(option.getWsId()) && option.isAutoTts()) {
                log.info("初始化TTS合成器，wsId：{}", option.getWsId());
                long start = System.currentTimeMillis();
                // 音色
                String voiceType = messageTtsCmdExe.findVoiceType(StrUtil.isNotBlank(context.getAgentId()) ? context.getConversationId() : null);
                if (StrUtil.isBlank(voiceType)) {
                    return;
                }
                // appKey
                String wsAppKey = appConfigProperties.extractWsAppKey();
                // sessionId
                String sessionId = String.format("%s_%s", context.getAnswerMsgId(), NanoIdUtil.simpleNanoId(6));

                LlmVoiceSynthesizerListener listener = new LlmVoiceSynthesizerListener(MDC.get(RequestConstant.REQUEST_ID));
                listener.setWsId(option.getWsId());
                listener.setSessionId(sessionId);
                listener.setMsgId(context.getAnswerMsgId());
                listener.setWsAppKey(wsAppKey);
                listener.addExt("source", TtsSourceEnum.CONVERSATION.getValue());
                listener.addExt("sessionId", sessionId);

                TTSSynthesizer ttsSynthesizer = initTTSSynthesizer(ttsClient, listener, voiceType);
                if (Objects.isNull(ttsSynthesizer)) {
                    log.warn("初始化TTS合成器失败，耗时：{}ms", System.currentTimeMillis() - start);
                    return;
                }

                // 设置到上下文
                context.setTtsSynthesizer(ttsSynthesizer);

                log.info("""
                    初始化TTS合成器成功，耗时：{}ms
                    sessionId：{}
                    """, System.currentTimeMillis() - start, sessionId);
            }

        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 流结束时执行的操作
     * @param taskRef 定时任务引用
     */
    private void flowableDoFinally(AtomicReference<ScheduledFuture<?>> taskRef) {
        try {
            // ①在流结束时取消定时任务
            log.debug("TTFT超时检测任务取消关闭");
            ScheduledFuture<?> task = taskRef.get();
            if (task != null && !task.isCancelled()) {
                task.cancel(true);
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 构建记忆上下文
     * @param model   模型
     * @param context 上下文
     * @return 记忆上下文
     */
    private LinkedList<ChatMessage> buildMemory(String model, ChatContext context) {
        LinkedList<ChatMessage> memory = new LinkedList<>();

        // 系统提示词
        if (!isReasoningModel(model)) {
            String sysPrompt = promptUtil.format(PromptParam.builder()
                .promptTmpl(dashScopeProperties.getChatDefaultConfig().getSystem())
                .question(context.getChatCmd().getQuestion())
                .ip(context.getIp())
                .build());

            memory.addFirst(ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(sysPrompt)
                .build()
            );
        }

        // 上下文
        memory.addAll(filterMemoryTurns(context.getHistory(), 6));

        return memory;
    }

    /**
     * 重写用户问题
     * @param context     上下文
     * @param ragResponse RAG返回内容
     * @return 结果
     */
    private String rewriteUserMsg(ChatContext context, RagResponse ragResponse) {
        if (ragResponse.hasResult()) {
            String promptTmpl = I18nUtil.isChinese() ? promptUtil.getRagUserPrompt() : promptUtil.getEnOnlineUserPrompt();
            // 附件
            if (ragResponse.isAttachmentNotEmpty()) {
                promptTmpl = promptUtil.getAttachmentUserPrompt();
            }
            PromptParam param = PromptParam.builder()
                .promptTmpl(promptTmpl)
                .ip(context.getIp())
                .question(context.getChatCmd().getQuestion())
                .ragResponse(ragResponse)
                .build();
            return promptUtil.format(param);
        }
        return context.getChatCmd().getQuestion();
    }

    /**
     * 获取会话记忆
     * @param conversationId 会话ID
     * @return 聊天记录
     */
    private LinkedList<ChatMessageEntity> listHistory(String conversationId) {
        List<ChatMessageEntity> list = chatMessageGateway.query(new MessageListQuery().setConversationId(conversationId));
        LinkedList<ChatMessageEntity> history = new LinkedList<>(list);
        // 1.剔除包含错误响应的问答
        history.removeIf(item -> Objects.nonNull(item.getExt()) && StringUtils.isNotBlank(item.getExt().getFailMsg()));
        // 2.TODO 根据聊天上下文信息过滤
        // cleanInvalidHead(history);
        // cleanInvalidContext(history);
        return history;
    }

    /**
     * 移除开头不是 user 或 system 的消息
     */
    private void cleanInvalidHead(LinkedList<ChatMessageEntity> history) {
        while (!history.isEmpty()) {
            String role = history.getFirst().getRole();
            if ("user".equals(role) || "system".equals(role)) {
                break;
            }
            history.removeFirst();
        }
    }

    /**
     * 校验并逐条修正聊天上下文记录
     */
    private void cleanInvalidContext(LinkedList<ChatMessageEntity> history) {
        if (history.isEmpty()) {
            return;
        }

        boolean startWithSystem = "system".equals(history.getFirst().getRole());

        int index = 0;
        while (index < history.size()) {
            String role = history.get(index).getRole();

            if (startWithSystem) {
                // system开头
                if (index == 0) {
                    if (!"system".equals(role)) {
                        history.remove(index);
                        continue;
                    }
                } else if (index == 1) {
                    if (!("user".equals(role) || "tool".equals(role))) {
                        history.remove(index);
                        continue;
                    }
                } else {
                    if ((index % 2 == 0 && !"assistant".equals(role)) ||
                        (index % 2 == 1 && !("user".equals(role) || "tool".equals(role)))) {
                        history.remove(index);
                        continue;
                    }
                }
            } else {
                // user开头
                if (index % 2 == 0) {
                    if (!("user".equals(role) || "tool".equals(role))) {
                        history.remove(index);
                        continue;
                    }
                } else {
                    if (!"assistant".equals(role)) {
                        history.remove(index);
                        continue;
                    }
                }
            }

            // 只有在没有删除的时候，才index++
            index++;
        }
    }

    /**
     * 创建SseEmitter
     * @return SseEmitter
     */
    public SseEmitter createEmitter() {
        SseEmitter emitter = new SseEmitter(9 * 60 * 1000L);
        emitter.onCompletion(() -> log.debug("emitter completed"));
        emitter.onError(throwable -> {
            MDC.put(RequestConstant.REQUEST_ID, MDC.get(RequestConstant.REQUEST_ID));
            handleEmitterError(throwable, emitter);
        });
        emitter.onTimeout(() -> {
            MDC.put(RequestConstant.REQUEST_ID, MDC.get(RequestConstant.REQUEST_ID));
            log.warn("emitter timeout");
        });
        return emitter;
    }

    /**
     * 前置校验
     * @param cmd 入参
     */
    private void preCheck(ChatCmd cmd, ConversationEntity conversationEntity) {
        // 0. 匿名会话检查
        if (Objects.nonNull(conversationEntity) && conversationEntity.isAnonymous()) {
            if (StringUtils.isBlank(cmd.getAnonymousKey()) || !Objects.equals(conversationEntity.getAnonymousKey(), cmd.getAnonymousKey())) {
                throw new NotPermissionException("会话不匹配");
            }
        }
        // 1.场景校验
        Assert.isTrue(BotCodeEnum.AI_SEARCH.eq(cmd.getBotCode()), I18nUtil.get(I18nValueEnum.CHAT_BOT_ERROR));

        // 2.知识库场景需要校验登录
        if (CollUtil.isNotEmpty(cmd.getFiles())
            || CollUtil.isNotEmpty(cmd.getReference())
            || (Objects.nonNull(cmd.getChatOption()) && cmd.getChatOption().isSearchKnowledge())) {
            StpUtil.checkLogin();
        }
    }

    protected String genDistributedId() {
        return externalCommonGateway.getDistributedId();
    }

    /**
     * 错误处理方法，统一处理 emitter 错误并完成 emitter
     * @param throwable 异常
     * @param emitter   SseEmitter
     */
    private void handleEmitterError(Throwable throwable, SseEmitter emitter) {
        log.warn(throwable.getMessage(), throwable);
        emitter.completeWithError(throwable);
    }

    /**
     * 过滤会话记忆
     * @param history 会话记忆
     * @param turns   轮数
     * @return 过滤后的会话记忆
     */
    public static LinkedList<ChatMessage> filterMemoryTurns(List<ChatMessageEntity> history, Integer turns) {
        if (CollUtil.isEmpty(history)) {
            return new LinkedList<>();
        }

        LinkedList<ChatMessage> recentMessages = new LinkedList<>();
        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
        for (ChatMessageEntity entity : history) {
            if (StringUtils.isBlank(entity.getContent())) {
                continue;
            }
            if (RoleEnum.USER.eq(entity.getRole())) {
                recentMessages.addFirst(ChatMessage.builder().role(ChatMessageRole.USER).content(entity.getContent()).build());
            }
            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
                recentMessages.addFirst(ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(entity.getContent()).build());
            }
            if (recentMessages.size() >= turns * 2) {
                break; // 找到N轮对话，退出
            }
        }
        return recentMessages;
    }

    protected RagResponse ragSearch(ChatContext ctx)  {
        // 1.联网搜索loading
        if (StrUtil.contains(ctx.getChatCmd().getUserAction(), "online")) {
            try {
                MessageModel.Msg notice = ctx.newSseMsg(MsgContentTypeEnum.PROGRESS, I18nUtil.get(I18nValueEnum.CHAT_WEB_SEARCHING));
                ctx.sendDeltaAnswerSSE(notice);
            } catch (Exception ignore) {}
        }

        RagResponse ragResp = new RagResponse();

        // 2.搜索附件
        AttachmentResponse attachmentResponse = handleAttachmentSearch(ctx);
        ragResp.setAttachmentResponse(attachmentResponse);

        // 3.先知识库搜索
        KnowledgeResponse knowledgeResponse = handleKnowledgeSearch(ctx);
        ragResp.setKnowledgeResponse(knowledgeResponse);

        // 4.再联网搜索
        SearchResponse searchResponse = handleCustomSearch(ctx, knowledgeResponse, attachmentResponse);
        ragResp.setSearchResponse(searchResponse);

        if (ragResp.isSearchNotEmpty() || ragResp.isKnowledgeNotEmpty()) {
            // 提前发送RAG搜索结果，缓解用户焦虑
            ctx.sendSearchCardSSE(ragResp);
        }
        return ragResp;
    }

    /**
     * 处理自定义搜索提示词
     * @param ctx 聊天上下文
     */
    protected SearchResponse handleCustomSearch(ChatContext ctx, KnowledgeResponse knowledgeResponse, AttachmentResponse attachmentResponse) {
        // 用户要求搜索，但是api不支持搜索
        SearchResponse customSearchResp = null;
        if (StrUtil.contains(ctx.getChatCmd().getUserAction(), "online")) {
            SplitSearchKeywordResult result = splitSearchKeywordCmdExe.execute(ctx, splicingAdditionInfo(ctx, knowledgeResponse, attachmentResponse));
            if (CollUtil.isNotEmpty(result.getSearchKeywords())) {
                customSearchResp = webSearchApi.webSearch(SearchRequest.builder()
                    .query(result.getSearchKeywords())
                    .build());
            }
        }
        return customSearchResp;
    }

    /**
     * 处理知识库搜索
     * @param ctx 聊天上下文
     */
    protected KnowledgeResponse handleKnowledgeSearch(ChatContext ctx) {
        if (!ctx.needSearchKnowledge()) {
            // 不需要检索场景，直接return
            return null;
        }

        String userId = UserContextUtil.getUserId();
        UserKnowledgeEntity userKnowledge = userKnowledgeGateway.getUserKnowledge(userId);
        if (Objects.isNull(userKnowledge)) {
            return null;
        }

        // 提取用户docList
        ChatCmd chatCmd = ctx.getChatCmd();
        List<BaseDocEntity> docInfos = extractDocInfos(chatCmd, userKnowledge.getKnowledgeId());
        if (CollUtil.isEmpty(docInfos)) {
            return null;
        }

        Map<String, BaseDocEntity> docExtMap = Maps.newHashMap();
        for (BaseDocEntity each : docInfos) {
            docExtMap.put(each.getDocIdExt(), each);
        }

        // 调用火山API检索
        LinkedList<ChatMessage> chatMessages = filterMemoryTurns(ctx.getHistory(), 2);
        chatMessages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(chatCmd.getQuestion()).build());

        KnowledgeRequest searchRequest = new KnowledgeRequest();
        searchRequest.setQuery(chatCmd.getQuestion());
        searchRequest.setKnowledgeIdExt(userKnowledge.getKnowledgeIdExt());
        searchRequest.setDocInfos(docInfos);
        searchRequest.setMessages(chatMessages);
        KnowledgeResponse searchResponse = knowledgeSearchApi.search(searchRequest);
        if (Objects.isNull(searchResponse) || CollUtil.isEmpty(searchResponse.getChunkList())) {
            return null;
        }

        // 遍历chunkList，将chunk聚合到对应的doc中
        LinkedHashMap<String, KnowledgeResponse.DocResult> docResultMap = Maps.newLinkedHashMap();
        for (KnowledgeResponse.ChunkResult chunk : searchResponse.getChunkList()) {
            String docIdExt = chunk.getDocIdExt();
            // 如果docResultMap中不存在该docIdExt，则创建一个新的DocResult
            if (!docResultMap.containsKey(docIdExt)) {
                BaseDocEntity docEntity = docExtMap.get(docIdExt);
                KnowledgeResponse.DocResult docResult = new KnowledgeResponse.DocResult();
                docResult.setDocIdExt(docIdExt);
                docResult.setChunkList(new ArrayList<>());
                if (Objects.nonNull(docEntity)) {
                    docResult.setDocId(docEntity.getDocId());
                    docResult.setDocType(docEntity.getDocType());
                    docResult.setDocName(docEntity.getDocName());
                    docResult.setDocSize(docEntity.getDocSize());
                    docResult.setWordNum(docEntity.getWordNum());
                }
                docResultMap.put(docIdExt, docResult);
            }

            // 获取对应的DocResult，并添加当前chunk
            KnowledgeResponse.DocResult docResult = docResultMap.get(docIdExt);
            docResult.getChunkList().add(chunk);
        }

        // 将map转换为list
        List<KnowledgeResponse.DocResult> aggDocList = new ArrayList<>(docResultMap.values());

        // 更新response的docList
        searchResponse.setDocList(aggDocList);
        return searchResponse;
    }


    private AttachmentResponse handleAttachmentSearch(ChatContext ctx) {
        if (!ctx.needSearchAttachment()) {
            // 不需要检索场景，直接return，附件带上
            return null;
        }
        // 所有的附件，包含引用的
        var attachments = ctx.getAttachments();
        AttachmentResponse attachmentResponse = new AttachmentResponse();

        try {
            MessageModel.Msg notice = ctx.newSseMsg(MsgContentTypeEnum.PROGRESS, I18nUtil.get(I18nValueEnum.CHAT_FILE_COMPREHENSION));
            ctx.sendDeltaAnswerSSE(notice);
        } catch (Exception ignore) {
        }

        // 文件附件
        Optional.ofNullable(extractFileAttachment(attachments)).ifPresent(attachmentResponse::setFileAttachmentResults);
        // 图片附件
        Optional.ofNullable(extractImageAttachment(attachments, ctx)).ifPresent(attachmentResponse::setImageAttachmentResult);
        return attachmentResponse;
    }

    private List<AttachmentResponse.FileAttachmentResult> extractFileAttachment(List<ChatCmd.FileItem> attachments) {
        if (CollUtil.isEmpty(attachments)) {
            return null;
        }
        // 非图片附件
        var fileAttachments = attachments.stream().filter(a -> docConfigHelper.isAttachmentSupportUnImageType(a.getFileType())).toList();

        var attachmentEntities = attachmentGateway.listByDocIds(fileAttachments.stream().map(ChatCmd.FileItem::getFileId).toList());
        attachmentEntities = attachmentEntities.stream().filter(a -> DocProcessStatusEnum.COMPLETED.getCode().equals(a.getProcessStatus())).toList();
        if (CollUtil.isNotEmpty(attachmentEntities)) {
            List<AttachmentResponse.FileAttachmentResult> fileAttachmentResults = new ArrayList<>();
            for (ConversationAttachmentEntity attachmentEntity : attachmentEntities) {
                AttachmentResponse.FileAttachmentResult result = new AttachmentResponse.FileAttachmentResult();
                result.setMeta(new AttachmentResponse.AttachmentMeta().setDocId(attachmentEntity.getDocId()).setDocName(attachmentEntity.getDocName()));
                result.setSummary(attachmentEntity.getSummary());
                fileAttachmentResults.add(result);
            }
            return fileAttachmentResults;
        }
        return null;
    }

    private AttachmentResponse.ImageAttachmentResult extractImageAttachment(List<ChatCmd.FileItem> attachments, ChatContext ctx) {
        if (CollUtil.isEmpty(attachments)) {
            return null;
        }
        // 筛选出图片附件
        var imageAttachments = attachments.stream().filter(a -> docConfigHelper.isAttachmentSupportImageType(a.getFileType())).toList();
        if (CollUtil.isEmpty(imageAttachments)) {
            return null;
        }
        var imageEntities = attachmentGateway.listByDocIds(imageAttachments.stream().map(ChatCmd.FileItem::getFileId).toList());
        List<String> imgUrls = new ArrayList<>();
        for (ConversationAttachmentEntity imageEntity : imageEntities) {
            imgUrls.add(docAccessHelper.getAttachmentDocAccessUrl(imageEntity));
        }
        var comprehension = visualComprehensionCmdExe.execute(filterMemoryTurns(ctx.getHistory(), 3), imgUrls, ctx.getChatCmd().getQuestion());
        if (StrUtil.isBlank(comprehension)) {
            return null;
        }
        AttachmentResponse.ImageAttachmentResult imageAttachmentResult = new AttachmentResponse.ImageAttachmentResult();
        imageAttachmentResult.setComprehension(comprehension);
        imageAttachmentResult.setMetas(imageEntities.stream()
            .map(i -> new AttachmentResponse.AttachmentMeta().setDocId(i.getDocId()).setDocName(i.getDocName())).collect(Collectors.toList()));
        return imageAttachmentResult;
    }

    private List<BaseDocEntity> extractDocInfos(ChatCmd chatCmd, String userKnowledgeId) {
        List<BaseDocEntity> docInfos = new ArrayList<>();
        // 所有知识库（个人+共享）
        if (Objects.nonNull(chatCmd.getChatOption())
            && chatCmd.getChatOption().isSearchAllKnowledge()
            && CollUtil.isEmpty(chatCmd.getFiles())
            && CollUtil.isEmpty(chatCmd.getReference())) {
            // 找出用户所有docInfo
            String userId = UserContextUtil.getUserId();
            // 个人知识库
            docInfos = userKnowledgeDocGateway.getUserAllDocIds(userId, userKnowledgeId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toList());
            // 共享知识库
            List<BaseDocEntity> allSharedDocs = sharedKnowledgeDocGateway.getUserAllDocIds(userId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toList());
            return new ArrayList<>(Stream.concat(docInfos.stream(), allSharedDocs.stream())
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toMap(
                    BaseDocEntity::getDocIdExt,
                    doc -> doc,
                    (existing, replacement) -> existing))
                .values());
        }
        // 所有个人知识库
        if (Objects.nonNull(chatCmd.getChatOption())
            && chatCmd.getChatOption().isSearchKnowledge()
            && CollUtil.isEmpty(chatCmd.getFiles())
            && CollUtil.isEmpty(chatCmd.getReference())) {
            // 找出用户所有docInfo
            String userId = UserContextUtil.getUserId();
            return userKnowledgeDocGateway.getUserAllDocIds(userId, userKnowledgeId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toList());
        }

        // 选择知识库的场景
        if (CollUtil.isNotEmpty(chatCmd.getKnowledgeList()) && CollUtil.isEmpty(chatCmd.getFiles())) {
            return processSelectKnowledge(chatCmd.getKnowledgeList(), userKnowledgeId);
        }

        // 筛选出用户指定的文档
        Set<String> docIds = new HashSet<>();
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            docIds.addAll(chatCmd.getFiles().stream()
                .map(ChatCmd.FileItem::getFileId)
                .filter(fileId -> StrUtil.startWith(fileId, CommonConst.Knowledge.DOC_PREFIX))
                .collect(Collectors.toSet())
            );
        }
        // 筛选出引用的文档
        if (CollUtil.isNotEmpty(chatCmd.getReference())) {
            docIds.addAll(chatCmd.getReference().stream()
                .map(ChatCmd.ReferenceItem::getFileId)
                .filter(fileId -> StrUtil.startWith(fileId, CommonConst.Knowledge.DOC_PREFIX))
                .collect(Collectors.toSet())
            );
        }

        if (CollUtil.isNotEmpty(docIds)) {
            docInfos = userKnowledgeDocGateway.listByDocIds(docIds).stream()
                .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                .collect(Collectors.toList());
        }

        // 筛选出用户指定的共享知识库的文档
        Set<String> shareDocIds = new HashSet<>();
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            docIds.addAll(chatCmd.getFiles().stream()
                .map(ChatCmd.FileItem::getFileId)
                .filter(fileId -> StrUtil.startWith(fileId, CommonConst.Knowledge.SHARE_DOC_PREFIX))
                .collect(Collectors.toSet())
            );
        }

        List<BaseDocEntity> sharedDocs = sharedKnowledgeDocGateway.listByDocIds(shareDocIds).stream()
            .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
            .collect(Collectors.toList());

        return new ArrayList<>(Stream.concat(docInfos.stream(), sharedDocs.stream())
            .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
            .collect(Collectors.toMap(
                BaseDocEntity::getDocIdExt,
                doc -> doc,
                (existing, replacement) -> existing))
            .values());
    }

    private List<BaseDocEntity> processSelectKnowledge(List<ChatCmd.KnowledgeItem> knowledgeList, String userKnowledgeId) {
        if (CollUtil.isEmpty(knowledgeList)) {
            return new ArrayList<>();
        }

        String userId = UserContextUtil.getUserId();
        List<BaseDocEntity> result = new ArrayList<>();

        // 按知识库类型进行分组
        Map<Integer, List<ChatCmd.KnowledgeItem>> groupedKnowledgeItems = knowledgeList.stream()
            .collect(Collectors.groupingBy(ChatCmd.KnowledgeItem::getKnowledgeType));

        // 处理个人知识库
        List<ChatCmd.KnowledgeItem> personalKnowledgeItems = groupedKnowledgeItems.get(KnowledgeTypeEnum.PERSONAL.getCode());
        if (CollUtil.isNotEmpty(personalKnowledgeItems)) {
            // 如果用户选择了自己的个人知识库，则加载所有文档
            if (personalKnowledgeItems.stream().anyMatch(item -> Objects.equals(item.getKnowledgeId(), userKnowledgeId))) {
                List<BaseDocEntity> personalDocs = userKnowledgeDocGateway.getUserAllDocIds(userId, userKnowledgeId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                    .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
                    .collect(Collectors.toList());
                result.addAll(personalDocs);
            }
        }

        // 处理共享知识库
        List<ChatCmd.KnowledgeItem> sharedKnowledgeItems = groupedKnowledgeItems.get(KnowledgeTypeEnum.SHARED.getCode());
        if (CollUtil.isNotEmpty(sharedKnowledgeItems)) {
            // 提取所有共享知识库ID
            List<String> sharedKnowledgeIds = sharedKnowledgeItems.stream()
                .map(ChatCmd.KnowledgeItem::getKnowledgeId)
                .distinct()
                .toList();

            // 直接使用SharedKnowledgeDocGateway的接口方法获取文档
            for (String knowledgeId : sharedKnowledgeIds) {
                // 获取指定知识库的所有文档
                List<BaseDocEntity> sharedDocs = sharedKnowledgeDocGateway.getUserAllDocIds(userId, knowledgeDocProperties.getSearchMaxDocSize()).stream()
                    .filter(doc -> Objects.equals(doc.getKnowledgeId(), knowledgeId) &&
                        DocProcessStatusEnum.COMPLETED.eq(doc.getProcessStatus()) &&
                        StrUtil.isNotBlank(doc.getDocIdExt()))
                    .collect(Collectors.toList());

                result.addAll(sharedDocs);
            }
        }

        // 确保结果中的文档唯一性（根据docIdExt字段去重）
        return new ArrayList<>(result.stream()
            .filter(i -> DocProcessStatusEnum.COMPLETED.eq(i.getProcessStatus()) && StrUtil.isNotBlank(i.getDocIdExt()))
            .collect(Collectors.toMap(
                BaseDocEntity::getDocIdExt,
                doc -> doc,
                (existing, replacement) -> existing,
                LinkedHashMap::new))
            .values());
    }


    private String splicingAdditionInfo(ChatContext ctx, KnowledgeResponse knowledgeResponse, AttachmentResponse attachmentResponse) {
        StringBuilder additionInfoBuilder = new StringBuilder();

        if (Objects.nonNull(knowledgeResponse) && CollUtil.isNotEmpty(knowledgeResponse.getChunkList())) {
            List<KnowledgeResponse.ChunkResult> sortList = knowledgeResponse.getChunkList().stream().limit(2).toList();
            additionInfoBuilder.append("知识库参考资料:");
            sortList.forEach(knowledge -> additionInfoBuilder.append("<doc_start>").append("\n").append(knowledge.getContent()).append("\n").append("<doc_end>"));
            additionInfoBuilder.append("\n\n").append("用户提问:").append(StrUtil.blankToDefault(knowledgeResponse.getRewriteQuery(), ctx.getChatCmd().getQuestion()));
            return additionInfoBuilder.toString();
        }

        //  用户当前提问，结合附件
        if (Objects.nonNull(attachmentResponse)) {
            if (CollUtil.isNotEmpty(attachmentResponse.getFileAttachmentResults())) {
                additionInfoBuilder.append("文件附件总结:");
                attachmentResponse.getFileAttachmentResults().forEach(fileAttachment -> additionInfoBuilder.append("<file_start>").append("\n").append(fileAttachment.getSummary()).append("\n").append("<file_end>"));
            }
            if (Objects.nonNull(attachmentResponse.getImageAttachmentResult())) {
                additionInfoBuilder.append("图片附件总结:");
                additionInfoBuilder.append("\n").append(attachmentResponse.getImageAttachmentResult().getComprehension());
            }
        }
        return additionInfoBuilder.toString();
    }

    private boolean isGreenIntercept(Exception ex) {
        return (ex instanceof ApiException || ex instanceof ArkHttpException) && StringUtils.contains(ex.getMessage(), "inappropriate content");
    }

    private boolean isTencentConcurrentRequest(Exception ex) {
        return ex instanceof TencentCloudSDKException && StringUtils.contains(ex.getMessage(), "20034");
    }

    private boolean isClientInterrupt(Exception ex) {
        return ex instanceof RuntimeException && ex.getCause() instanceof ClientAbortException;
    }

    private boolean isBrokenPipe(Exception ex) {
        return ex instanceof ClientAbortException && ex.getCause() instanceof IOException
            && StrUtil.contains(ex.getMessage(), "Broken pipe");
    }

    private static boolean isAsyncTimeout(Exception ex) {
        return ex instanceof IllegalStateException && StringUtils.contains(ex.getMessage(), "already completed");
    }

    private void handleException(Exception ex, ChatContext context) {
        if (isGreenIntercept(ex)) { // 绿网
            context.sendGreenInterceptSSE();
        } else if (isTencentConcurrentRequest(ex)) { // 腾讯并发请求
            context.sendConcurrentRequestSSE();
        } else if (isAsyncTimeout(ex)) { // 请求超时，超过Emitter的超时时间
            log.warn("请求超时", ex);
        } else if (isClientInterrupt(ex)) {
            log.debug("流式对话被打断：{}", ex.getMessage(), ex);
        } else if (isBrokenPipe(ex)) {
            log.debug("流式对话异常Broken pipe：{}", ex.getMessage(), ex);
        } else {
            log.error(ex.getMessage(), ex);
        }
        context.getEmitter().completeWithError(ex);
    }

    private boolean isFirstToken(ChatMessage message, ChatContext context) {
        if (Objects.isNull(context.getUsageInfo()) || Objects.isNull(context.getUsageInfo().getFirstTokenCostMs())) {
            return isOutputAnswer(message) || isOutputThinking(message);
        }
        return false;
    }

    private boolean isOutputThinking(ChatMessage message) {
        return Objects.nonNull(message) && StrUtil.isNotEmpty(message.getReasoningContent());
    }

    private boolean isOutputAnswer(ChatMessage message) {
        return Objects.nonNull(message) && StrUtil.isNotEmpty(Convert.toStr(message.getContent()));
    }

}
