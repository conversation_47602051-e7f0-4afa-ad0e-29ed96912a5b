//package com.dangbei.aisearch.app.extension;
//
//import cn.hutool.core.collection.CollUtil;
//import com.alibaba.cola.common.constant.RequestConstant;
//import com.alibaba.dashscope.common.Message;
//import com.alibaba.dashscope.common.Role;
//import com.alibaba.dashscope.exception.ApiException;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson2.JSONObject;
//import com.coze.openapi.client.connversations.message.model.MessageRole;
//import com.coze.openapi.client.connversations.message.model.MessageType;
//import com.dangbei.aisearch.app.assembler.ChatMessageAssembler;
//import com.dangbei.aisearch.app.executor.ConversationFirstAnswerContentCmdExe;
//import com.dangbei.aisearch.app.executor.SuggestCmdExe;
//import com.dangbei.aisearch.app.executor.SummaryConversationTitleCmdExe;
//import com.dangbei.aisearch.app.extensionpoint.ChatSceneExtPt;
//import com.dangbei.aisearch.app.model.MessageModel;
//import com.dangbei.aisearch.client.dto.ChatMsgExt;
//import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
//import com.dangbei.aisearch.common.enums.RoleEnum;
//import com.dangbei.aisearch.common.util.ObjectMapperUtil;
//import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
//import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
//import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
//import com.tencentcloudapi.common.exception.TencentCloudSDKException;
//import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
//import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.catalina.connector.ClientAbortException;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.MDC;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.CompletableFuture;
//import java.util.concurrent.Executor;
//
//import static com.alibaba.cola.common.constant.RequestConstant.REQUEST_ID;
//
///**
// * 聊天对话扩展点抽象类
// * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2025-01-18
// */
//@Slf4j
//@Component
//public abstract class AbstractChatExt implements ChatSceneExtPt {
//
//    @Resource(name = "chatAsyncExecutor")
//    protected Executor asyncExecutor;
//    @Resource
//    protected SuggestCmdExe suggestCmdExe;
//    @Resource
//    protected ChatMessageGateway chatMessageGateway;
//    @Resource
//    protected ExternalCommonGateway externalCommonGateway;
//    @Resource
//    protected ChatMessageAssembler chatMessageAssembler;
//    @Resource
//    protected SummaryConversationTitleCmdExe summaryConversationTitleCmdExe;
//    @Resource
//    protected ConversationFirstAnswerContentCmdExe conversationFirstAnswerContentCmdExe;
//    @Resource(name = "saveChatMessageAsyncExecutor")
//    protected Executor saveChatMessageAsyncExecutor;
//
//    /**
//     * 流式对话抽象方法，子类实现
//     * @param ctx 聊天上下文
//     */
//    public abstract void chatCompletion(ChatContext ctx) throws Exception;
//
//    @Override
//    public void streamChat(ChatContext context) {
//        // ①发送loading，缓解用户焦虑
//        sendLoading(context);
//
//        // ②一些前置操作，若有
//        preHandle(context);
//
//        // ③异步流式调用
//        CompletableFuture.runAsync(() -> {
//            try {
//                context.setQuestionMsgId(externalCommonGateway.getDistributedId());
//                context.setAnswerMsgId(externalCommonGateway.getDistributedId());
//
//                // 调用具体大模型流式对话，需要自行维护回答、上下文的拼接
//                chatCompletion(context);
//
//                // 推荐问题
//                context.addFollowUp(sendSuggestMessage(context));
//                context.sendChatCompletedSSE();
//            } catch (ApiException e) {
//                if (StringUtils.contains(e.getMessage(), "inappropriate content")) {
//                    try {
//                        context.setFailMsg("涉及敏感话题");
//                        context.sendDeltaAnswerSSE("尊敬的用户您好，让我们换个话题再聊聊吧！");
//                        context.getMessageModel().setFullMsg(new StringBuffer("尊敬的用户您好，让我们换个话题再聊聊吧！"));
//                    } catch (IOException ex) {
//                        log.debug(ex.getMessage(), ex);
//                    }
//                } else {
//                    log.error(e.getMessage(), e);
//                }
//            } catch (TencentCloudSDKException ex) {
//                if (StringUtils.contains(ex.getMessage(), "20034")) {
//                    try {
//                        context.setFailMsg("TencentCloudSDKException");
//                        context.sendDeltaAnswerSSE("系统繁忙中，请稍后再试");
//                        context.getMessageModel().setFullMsg(new StringBuffer("系统繁忙中，请稍后再试"));
//                    } catch (IOException err) {
//                        log.debug(err.getMessage(), err);
//                    }
//                } else {
//                    log.error(ex.getMessage(), ex);
//                }
//            } catch (Exception ex) {
//                if (ex instanceof RuntimeException && ex.getCause() instanceof ClientAbortException) {
//                    log.debug("流式对话被打断：{}", ex.getMessage(), ex);
//                } else {
//                    log.error(ex.getMessage(), ex);
//                }
//            } finally {
//                try {
//                    context.getEmitter().complete();
//                } catch (Exception e) {
//                    log.error(e.getMessage(), e);
//                }
//
//                // 存储聊天记录
//                storeChatMsg(context);
//            }
//
//        }, asyncExecutor);
//    }
//
//    protected void sendLoading(ChatContext context) {
//        // empty
//    }
//
//    protected void preHandle(ChatContext context) {
//        // empty
//    }
//
//    protected Boolean answerIsDeep(ChatContext context) {
//        return Boolean.FALSE;
//    }
//
//    @SneakyThrows
//    protected List<String> sendSuggestMessage(ChatContext ctx) {
//        // 推一条loading
//        ctx.sendSSE("conversation.followup.loading", new JSONObject());
//        // 生成推荐问题
//        ChatMessageEntity userMsg = new ChatMessageEntity();
//        userMsg.setRole(RoleEnum.USER.getRoleType());
//        userMsg.setContent(ctx.getChatCmd().getQuestion());
//        ChatMessageEntity aiMsg = new ChatMessageEntity();
//        aiMsg.setRole(RoleEnum.ASSISTANT.getRoleType());
//        aiMsg.setContent(ctx.getFullMsg());
//        // 添加进回答
//        ctx.getHistory().addFirst(userMsg);
//        ctx.getHistory().addFirst(aiMsg);
//        List<String> suggests = suggestCmdExe.execute(ctx.getHistory());
//        if (CollUtil.isNotEmpty(suggests)) {
//            for (String text : suggests) {
//                MessageModel.Msg msg = new MessageModel.Msg();
//                msg.setType(MessageType.FOLLOW_UP.getValue());
//                msg.setRole(MessageRole.ASSISTANT.getValue());
//                msg.setContentType(MsgContentTypeEnum.FOLLOW_UP.getValue());
//                msg.setContent(text);
//                msg.setRequestId(MDC.get(REQUEST_ID));
//                ctx.sendSSE("conversation.message.completed", ObjectMapperUtil.toJson(msg));
//            }
//        }
//        return suggests;
//    }
//
//    /**
//     * 存储聊天记录
//     * @param ctx 聊天上下文
//     */
//    protected void storeChatMsg(ChatContext ctx) {
//        List<ChatMessageEntity> messageList = new ArrayList<>(2);
//        String chatId = externalCommonGateway.getDistributedId();
//
//        // 提问Entity
//        ChatMessageEntity question = chatMessageAssembler.toUserMessageEntity(ctx.getChatCmd(), ctx.getQuestionMsgId());
//        question.setChatId(chatId);
//        question.setConversationId(ctx.getConversationId());
//        if (StringUtils.isNotBlank(ctx.getFailMsg()) && Objects.nonNull(question.getExt())) {
//            question.getExt().setFailMsg(ctx.getFailMsg());
//        }
//        messageList.add(question);
//
//        // 回答Entity
//        if (!StringUtils.isAllBlank(ctx.getThinkMsg(), ctx.getFullMsg())) {
//            ChatMessageEntity answer = chatMessageAssembler.toAssistantMessageEntity(ctx.getChatCmd());
//            answer.setMsgId(ctx.getAnswerMsgId());
//            answer.setChatId(chatId);
//            answer.setConversationId(ctx.getConversationId());
//            answer.setContent(ctx.getFullMsg());
//            answer.setContentType(MsgContentTypeEnum.TEXT.getValue());
//
//            // 一些扩展信息
//            ChatMsgExt chatMsgExt = new ChatMsgExt();
//            chatMsgExt.setIsDeep(answerIsDeep(ctx));
//            chatMsgExt.setRequestId(MDC.get(RequestConstant.REQUEST_ID));
//            chatMsgExt.setFollowUp(JSON.toJSONString(ctx.getFollowUp()));
//            chatMsgExt.setSearchReferences(Objects.isNull(ctx.getSearchReferences()) ? null : JSON.toJSONString(ctx.getSearchReferences()));
//            chatMsgExt.setThinking(ctx.getThinkMsg());
//            chatMsgExt.setTokenUsageCo(ctx.getUsageInfo());
//            chatMsgExt.setRequestIdExt(ctx.getRequestIdExt());
//            if (StringUtils.isNotBlank(ctx.getFailMsg())) {
//                chatMsgExt.setFailMsg(ctx.getFailMsg());
//            }
//            answer.setExt(chatMsgExt);
//
//            // token消耗信息
//            if (Objects.nonNull(ctx.getUsageInfo())) {
//                answer.setLlmProvider(ctx.getUsageInfo().getProvider());
//                answer.setModel(ctx.getUsageInfo().getModel());
//                answer.setTotalTokens(ctx.getUsageInfo().getTotalTokens());
//                answer.setInputTokens(ctx.getUsageInfo().getInputTokens());
//                answer.setOutputTokens(ctx.getUsageInfo().getOutputTokens());
//                answer.setCostTime(ctx.getUsageInfo().getCostTime());
//                answer.setFirstTokenCostMs(ctx.getUsageInfo().getFirstTokenCostMs());
//            }
//
//            long currTimeStamp = System.currentTimeMillis() / 1000;
//            answer.setCreatedAt(currTimeStamp);
//            answer.setUpdatedAt(currTimeStamp);
//            messageList.add(answer);
//        }
//        saveChatMessageAsyncExecutor.execute(() -> {
//            chatMessageGateway.insertBatch(messageList);
//            // 总结会话标题
//            summaryConversationTitleCmdExe.execute(ctx.getConversationId());
//            // 将首次回答内容记录到会话表
//            conversationFirstAnswerContentCmdExe.execute(ctx.getConversationId());
//        });
//    }
//
//    public static List<ChatMessage> getRecentTurns(List<ChatMessageEntity> history, Integer turns) {
//        if (CollUtil.isEmpty(history)) {
//            return new ArrayList<>();
//        }
//
//        LinkedList<ChatMessage> recentMessages = new LinkedList<>();
//        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
//        for (ChatMessageEntity entity : history) {
//            if (RoleEnum.USER.eq(entity.getRole())) {
//                recentMessages.addFirst(ChatMessage.builder().role(ChatMessageRole.USER).content(entity.getContent()).build());
//            }
//            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
//                recentMessages.addFirst(ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(entity.getContent()).build());
//            }
//            if (recentMessages.size() >= turns * 2) {
//                break; // 找到三轮对话，退出
//            }
//        }
//        return recentMessages;
//    }
//
//    public static List<Message> getRecentMsgTurns(List<ChatMessageEntity> history, Integer turns) {
//        if (CollUtil.isEmpty(history)) {
//            return new ArrayList<>();
//        }
//
//        LinkedList<Message> recentMessages = new LinkedList<>();
//        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
//        for (ChatMessageEntity entity : history) {
//            if (StringUtils.isBlank(entity.getContent())) {
//                continue;
//            }
//            if (RoleEnum.USER.eq(entity.getRole())) {
//                recentMessages.addFirst(Message.builder().role(Role.USER.getValue()).content(entity.getContent()).build());
//            }
//            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
//                recentMessages.addFirst(Message.builder().role(Role.ASSISTANT.getValue()).content(entity.getContent()).build());
//            }
//            if (recentMessages.size() >= turns * 2) {
//                break; // 找到三轮对话，退出
//            }
//        }
//        return recentMessages;
//    }
//
//    public static List<com.tencentcloudapi.lkeap.v20240522.models.Message> getRecentMsgTencent(List<ChatMessageEntity> history, Integer turns) {
//        if (CollUtil.isEmpty(history)) {
//            return new ArrayList<>();
//        }
//
//        LinkedList<com.tencentcloudapi.lkeap.v20240522.models.Message> recentMessages = new LinkedList<>();
//        // 倒序遍历消息列表，找到最近的 12 条 user/assistant 消息（6轮）
//        for (ChatMessageEntity entity : history) {
//            if (RoleEnum.USER.eq(entity.getRole())) {
//                com.tencentcloudapi.lkeap.v20240522.models.Message msg = new com.tencentcloudapi.lkeap.v20240522.models.Message();
//                msg.setRole(Role.USER.getValue());
//                msg.setContent(entity.getContent());
//                recentMessages.addFirst(msg);
//            }
//            if (RoleEnum.ASSISTANT.eq(entity.getRole())) {
//                com.tencentcloudapi.lkeap.v20240522.models.Message msg = new com.tencentcloudapi.lkeap.v20240522.models.Message();
//                msg.setRole(Role.ASSISTANT.getValue());
//                msg.setContent(entity.getContent());
//                recentMessages.addFirst(msg);
//            }
//            if (recentMessages.size() >= turns * 2) {
//                break; // 找到三轮对话，退出
//            }
//        }
//        return recentMessages;
//    }
//
//}
