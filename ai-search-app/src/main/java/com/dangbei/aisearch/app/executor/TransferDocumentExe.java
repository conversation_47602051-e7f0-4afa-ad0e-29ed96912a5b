package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.TransferDocumentResultCo;
import com.dangbei.aisearch.client.dto.cmd.TransferDocumentCmd;
import com.dangbei.aisearch.client.enums.SharedKnowledgeDocTransferTypeEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.client.enums.KnowledgeTypeEnum;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeDocEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeDocEntity;
import com.dangbei.aisearch.domain.entity.UserKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeDocGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 文档转移执行器
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Component
public class TransferDocumentExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private SharedKnowledgeDocGateway sharedKnowledgeDocGateway;
    @Resource
    private UserKnowledgeGateway userKnowledgeGateway;
    @Resource
    private UserKnowledgeDocGateway userKnowledgeDocGateway;
    @Resource
    private ExternalCommonGateway externalCommonGateway;

    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<TransferDocumentResultCo> execute(TransferDocumentCmd cmd) {
        String currentUserId = UserContextUtil.getNonNullUserId();
        // 验证转移类型
        SharedKnowledgeDocTransferTypeEnum transferType = SharedKnowledgeDocTransferTypeEnum.getByCode(cmd.getTransferType());
        Assert.notNull(transferType, "无效的操作类型");
        if (KnowledgeTypeEnum.isShared(cmd.getSourceKnowledge().getKnowledgeType())) {
            Assert.notNull(cmd.getSourceKnowledge().getKnowledgeId(), "共享知识库id不能为空");
        }
        if (KnowledgeTypeEnum.isShared(cmd.getTargetKnowledge().getKnowledgeType())) {
            Assert.notNull(cmd.getTargetKnowledge().getKnowledgeId(), "共享知识库id不能为空");
        }
        // 根据知识库类型选择不同的转移策略
        if (isPersonalToShared(cmd)) {
            return transferPersonalToShared(cmd, currentUserId, transferType);
        } else if (isSharedToPersonal(cmd)) {
            return transferSharedToPersonal(cmd, currentUserId, transferType);
        } else if (isSharedToShared(cmd)) {
            return transferSharedToShared(cmd, currentUserId, transferType);
        } else if (isPersonalToPersonal(cmd)) {
            throw new BizException("个人知识库间不支持文档转移");
        } else {
            throw new BizException("不支持的转移类型");
        }
    }

    /**
     * 个人知识库到共享知识库
     */
    private SingleResponse<TransferDocumentResultCo> transferPersonalToShared(
            TransferDocumentCmd cmd, String currentUserId, SharedKnowledgeDocTransferTypeEnum transferType) {
        // 验证源知识库权限
        UserKnowledgeEntity sourceKnowledge = userKnowledgeGateway.getUserKnowledge(currentUserId);
        Assert.notNull(sourceKnowledge, "源知识库不存在");
        Assert.equals(sourceKnowledge.getUserId(), currentUserId, "无权限访问源知识库");
        // 验证目标知识库权限
        SharedKnowledgeEntity targetKnowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getTargetKnowledge().getKnowledgeId());
        Assert.notNull(targetKnowledge, "目标知识库不存在");
        targetKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.UPLOAD_DOC);
        // 获取源文档
        List<UserKnowledgeDocEntity> sourceDocuments = userKnowledgeDocGateway.listByDocIds(cmd.getDocIds());
        Assert.notEmpty(sourceDocuments, "未找到要转移的文档");

        List<String> successDocIds = new ArrayList<>();
        List<String> failedDocIds = new ArrayList<>();

        for (UserKnowledgeDocEntity sourceDoc : sourceDocuments) {
            try {
                // 验证文档归属
                Assert.equals(sourceDoc.getKnowledgeId(), sourceKnowledge.getKnowledgeId(), "文档不属于源知识库");
                // 校验文档状态，只允许转移处理完成的文档
                if (!Objects.equals(sourceDoc.getProcessStatus(), DocProcessStatusEnum.COMPLETED.getCode())) {
                    throw new BizException("只能转移处理完成的文档");
                }
                // 创建共享知识库文档
                SharedKnowledgeDocEntity targetDoc = createSharedKnowledgeDoc(sourceDoc, targetKnowledge, currentUserId);
                targetDoc.save();
                // 如果是移动操作，删除源文档
                if (transferType == SharedKnowledgeDocTransferTypeEnum.MOVE) {
                    sourceDoc.delete();
                }
                successDocIds.add(sourceDoc.getDocId());
            } catch (Exception e) {
                log.warn("文档转移失败：docId={}, error={}", sourceDoc.getDocId(), e.getMessage(), e);
                failedDocIds.add(sourceDoc.getDocId());
            }
        }
        return SingleResponse.of(TransferDocumentResultCo.builder()
            .successCount(successDocIds.size())
            .failedCount(failedDocIds.size())
            .successDocIds(successDocIds)
            .failedDocIds(failedDocIds)
            .build());
    }

    /**
     * 共享知识库到个人知识库
     */
    private SingleResponse<TransferDocumentResultCo> transferSharedToPersonal(
            TransferDocumentCmd cmd, String currentUserId, SharedKnowledgeDocTransferTypeEnum transferType) {
        // 验证源知识库权限
        SharedKnowledgeEntity sourceKnowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getSourceKnowledge().getKnowledgeId());
        Assert.notNull(sourceKnowledge, "源知识库不存在");
        sourceKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.DOWNLOAD_DOC);
        // 如果是移动操作，需要检查删除权限
        if (transferType == SharedKnowledgeDocTransferTypeEnum.MOVE) {
            sourceKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.DELETE_DOC);
        }
        // 获取或创建个人知识库
        UserKnowledgeEntity targetKnowledge = userKnowledgeGateway.getOrCreateKnowledge(currentUserId, null);
        // 获取源文档
        List<SharedKnowledgeDocEntity> sourceDocuments = sharedKnowledgeDocGateway.listByDocIds(cmd.getDocIds());
        Assert.notEmpty(sourceDocuments, "未找到要转移的文档");

        List<String> successDocIds = new ArrayList<>();
        List<String> failedDocIds = new ArrayList<>();

        for (SharedKnowledgeDocEntity sourceDoc : sourceDocuments) {
            try {
                // 验证文档归属
                Assert.equals(sourceDoc.getKnowledgeId(), sourceKnowledge.getKnowledgeId(), "文档不属于源知识库");
                // 校验文档状态，只允许转移处理完成的文档
                if (!Objects.equals(sourceDoc.getProcessStatus(), DocProcessStatusEnum.COMPLETED.getCode())) {
                    throw new BizException("只能转移处理完成的文档");
                }
                // 创建个人知识库文档
                UserKnowledgeDocEntity targetDoc = createUserKnowledgeDoc(sourceDoc, targetKnowledge);
                targetDoc.save();
                // 如果是移动操作，删除源文档
                if (transferType == SharedKnowledgeDocTransferTypeEnum.MOVE) {
                    sourceDoc.delete();
                }
                successDocIds.add(sourceDoc.getDocId());
            } catch (Exception e) {
                log.warn("文档转移失败：docId={}, error={}", sourceDoc.getDocId(), e.getMessage(), e);
                failedDocIds.add(sourceDoc.getDocId());
            }
        }
        return SingleResponse.of(TransferDocumentResultCo.builder()
            .successCount(successDocIds.size())
            .failedCount(failedDocIds.size())
            .successDocIds(successDocIds)
            .failedDocIds(failedDocIds)
            .build());
    }

    /**
     * 共享知识库到共享知识库
     */
    private SingleResponse<TransferDocumentResultCo> transferSharedToShared(
            TransferDocumentCmd cmd, String currentUserId, SharedKnowledgeDocTransferTypeEnum transferType) {
        // 验证源知识库权限
        SharedKnowledgeEntity sourceKnowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getSourceKnowledge().getKnowledgeId());
        Assert.notNull(sourceKnowledge, "源知识库不存在");
        sourceKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.DOWNLOAD_DOC);
        // 如果是移动操作，需要检查删除权限
        if (transferType == SharedKnowledgeDocTransferTypeEnum.MOVE) {
            sourceKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.DELETE_DOC);
        }
        // 验证目标知识库权限
        SharedKnowledgeEntity targetKnowledge = sharedKnowledgeGateway.getByKnowledgeId(cmd.getTargetKnowledge().getKnowledgeId());
        Assert.notNull(targetKnowledge, "目标知识库不存在");
        targetKnowledge.validatePermission(currentUserId, SharedKnowledgePermissionEnum.UPLOAD_DOC);
        // 检查是否是同一个知识库
        if (Objects.equals(sourceKnowledge.getKnowledgeId(), targetKnowledge.getKnowledgeId())) {
            throw new BizException("源知识库和目标知识库不能相同");
        }
        // 获取源文档
        List<SharedKnowledgeDocEntity> sourceDocuments = sharedKnowledgeDocGateway.listByDocIds(cmd.getDocIds());
        Assert.notEmpty(sourceDocuments, "未找到要转移的文档");

        List<String> successDocIds = new ArrayList<>();
        List<String> failedDocIds = new ArrayList<>();

        for (SharedKnowledgeDocEntity sourceDoc : sourceDocuments) {
            try {
                // 验证文档归属
                Assert.equals(sourceDoc.getKnowledgeId(), sourceKnowledge.getKnowledgeId(), "文档不属于源知识库");
                // 校验文档状态，只允许转移处理完成的文档
                if (!Objects.equals(sourceDoc.getProcessStatus(), DocProcessStatusEnum.COMPLETED.getCode())) {
                    throw new BizException("只能转移处理完成的文档");
                }
                // 创建目标文档
                SharedKnowledgeDocEntity targetDoc = copySharedKnowledgeDoc(sourceDoc, targetKnowledge, currentUserId);
                targetDoc.save();
                // 如果是移动操作，删除源文档
                if (transferType == SharedKnowledgeDocTransferTypeEnum.MOVE) {
                    sourceDoc.delete();
                }
                successDocIds.add(sourceDoc.getDocId());
            } catch (Exception e) {
                log.warn("文档转移失败：docId={}, error={}", sourceDoc.getDocId(), e.getMessage(), e);
                failedDocIds.add(sourceDoc.getDocId());
            }
        }
        return SingleResponse.of(TransferDocumentResultCo.builder()
            .successCount(successDocIds.size())
            .failedCount(failedDocIds.size())
            .successDocIds(successDocIds)
            .failedDocIds(failedDocIds)
            .build());
    }

    // ==================== 辅助方法 ====================

    private boolean isPersonalToShared(TransferDocumentCmd cmd) {
        return KnowledgeTypeEnum.isPersonal(cmd.getSourceKnowledge().getKnowledgeType()) &&
            KnowledgeTypeEnum.isShared(cmd.getTargetKnowledge().getKnowledgeType());
    }

    private boolean isSharedToPersonal(TransferDocumentCmd cmd) {
        return KnowledgeTypeEnum.isShared(cmd.getSourceKnowledge().getKnowledgeType()) &&
            KnowledgeTypeEnum.isPersonal(cmd.getTargetKnowledge().getKnowledgeType());
    }

    private boolean isSharedToShared(TransferDocumentCmd cmd) {
        return KnowledgeTypeEnum.isShared(cmd.getSourceKnowledge().getKnowledgeType()) &&
            KnowledgeTypeEnum.isShared(cmd.getTargetKnowledge().getKnowledgeType());
    }

    private boolean isPersonalToPersonal(TransferDocumentCmd cmd) {
        return KnowledgeTypeEnum.isPersonal(cmd.getSourceKnowledge().getKnowledgeType()) &&
            KnowledgeTypeEnum.isPersonal(cmd.getTargetKnowledge().getKnowledgeType());
    }

    /**
     * 从个人知识库文档创建共享知识库文档
     */
    private SharedKnowledgeDocEntity createSharedKnowledgeDoc(UserKnowledgeDocEntity source,
                                                               SharedKnowledgeEntity targetKnowledge,
                                                               String currentUserId) {
        SharedKnowledgeDocEntity target = new SharedKnowledgeDocEntity();
        target.setKnowledgeId(targetKnowledge.getKnowledgeId());
        target.setDocId(CommonConst.Knowledge.SHARE_DOC_PREFIX + externalCommonGateway.getDistributedId());
        target.setDocName(source.getDocName());
        target.setDocPath(source.getDocPath());
        target.setDocType(source.getDocType());
        target.setDocSize(source.getDocSize());
        target.setMd5(source.getMd5());
        target.setProcessStatus(source.getProcessStatus());
        target.setPointNum(source.getPointNum());
        target.setWordNum(source.getWordNum());
        target.setDocIdExt(source.getDocIdExt());
        target.setSummary(source.getSummary());
        target.setUploadUserId(currentUserId);
        target.setStorageType(source.getStorageType());
        return target;
    }

    /**
     * 从共享知识库文档创建个人知识库文档
     */
    private UserKnowledgeDocEntity createUserKnowledgeDoc(SharedKnowledgeDocEntity source,
                                                           UserKnowledgeEntity targetKnowledge) {
        UserKnowledgeDocEntity target = new UserKnowledgeDocEntity();
        target.setKnowledgeId(targetKnowledge.getKnowledgeId());
        target.setDocId(CommonConst.Knowledge.DOC_PREFIX + externalCommonGateway.getDistributedId());
        target.setDocName(source.getDocName());
        target.setDocPath(source.getDocPath());
        target.setDocType(source.getDocType());
        target.setDocSize(source.getDocSize());
        target.setMd5(source.getMd5());
        target.setProcessStatus(source.getProcessStatus());
        target.setPointNum(source.getPointNum());
        target.setWordNum(source.getWordNum());
        target.setDocIdExt(source.getDocIdExt());
        target.setSummary(source.getSummary());
        target.setStorageType(source.getStorageType());
        return target;
    }

    /**
     * 复制共享知识库文档
     */
    private SharedKnowledgeDocEntity copySharedKnowledgeDoc(SharedKnowledgeDocEntity source,
                                                             SharedKnowledgeEntity targetKnowledge,
                                                             String currentUserId) {
        SharedKnowledgeDocEntity target = new SharedKnowledgeDocEntity();
        target.setKnowledgeId(targetKnowledge.getKnowledgeId());
        target.setDocId(CommonConst.Knowledge.SHARE_DOC_PREFIX + externalCommonGateway.getDistributedId());
        target.setDocName(source.getDocName());
        target.setDocPath(source.getDocPath());
        target.setDocType(source.getDocType());
        target.setDocSize(source.getDocSize());
        target.setMd5(source.getMd5());
        target.setProcessStatus(source.getProcessStatus());
        target.setPointNum(source.getPointNum());
        target.setWordNum(source.getWordNum());
        target.setDocIdExt(source.getDocIdExt());
        target.setSummary(source.getSummary());
        target.setUploadUserId(currentUserId);
        target.setStorageType(source.getStorageType());
        return target;
    }
}
