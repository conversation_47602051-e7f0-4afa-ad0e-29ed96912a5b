package com.dangbei.aisearch.app.util;

import cn.hutool.extra.spring.SpringUtil;
import com.dangbei.aisearch.client.enums.KnowledgeApplyStatusEnum;
import com.dangbei.aisearch.client.enums.UserApplyStatusEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeApplyGateway;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;

/**
 * 知识库申请工具类
 * <AUTHOR>
 * @date 2025-05-27
 **/
@UtilityClass
public class KnowledgeApplyUtil {

    private static final KnowledgeApplyGateway knowledgeApplyGateway = SpringUtil.getBean(KnowledgeApplyGateway.class);

    /**
     * 获取用户对知识库的申请状态
     * @param knowledgeId 知识库ID
     * @param userId 用户ID
     * @param isMember 是否已是成员
     * @return 申请状态枚举
     */
    public UserApplyStatusEnum getUserApplyStatus(String knowledgeId, String userId, boolean isMember) {
        if (StringUtils.isAnyBlank(knowledgeId, userId)) {
            return UserApplyStatusEnum.NOT_APPLIED;
        }

        // 如果已经是成员，返回已通过
        if (isMember) {
            return UserApplyStatusEnum.APPROVED;
        }

        // 直接查询用户对该知识库的申请记录，无需分页
        KnowledgeApplyEntity pendingApply = knowledgeApplyGateway.getPendingApply(knowledgeId, userId);
        if (pendingApply != null) {
            return UserApplyStatusEnum.PENDING;
        }

        // 查询该用户对该知识库的所有申请记录
        List<KnowledgeApplyEntity> applyList = knowledgeApplyGateway.listUserAppliesForKnowledge(knowledgeId, userId);

        if (applyList.isEmpty()) {
            return UserApplyStatusEnum.NOT_APPLIED;
        }

        // 按创建时间倒序排列，获取最新的申请
        KnowledgeApplyEntity latestApply = applyList.stream().max(Comparator.comparing(KnowledgeApplyEntity::getCreateTime))
                .orElse(null);

        // 映射申请状态到用户申请状态
        KnowledgeApplyStatusEnum status = latestApply.getStatusEnum();
        return switch (status) {
            case PENDING -> UserApplyStatusEnum.PENDING;
            // 非成员，且最后一条为申请通过记录，则说明通过后退出，返回未申请
            case APPROVED -> UserApplyStatusEnum.NOT_APPLIED;
            case REJECTED -> UserApplyStatusEnum.REJECTED;
        };
    }
}
