package com.dangbei.aisearch.app.modelchat.volc;

import com.alibaba.cola.extension.Extension;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.config.properties.VolcEngineArkProperties;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.SystemMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;

/**
 * 月之暗面
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-14
 */
@Slf4j
@Component("moonshot_volc_model_chat")
@Extension(bizId = CommonConst.ModelIntent.MOONSHOT_V1)
public class Moonshot extends AbstractVolcModelChatExt {

    @Resource
    protected DashScopeProperties dashScopeProperties;
    @Resource
    private VolcEngineArkProperties volcEngineArkProperties;

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        String sysPrompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(dashScopeProperties.getChatDefaultConfig().getSystem())
            .question(ctx.getChatCmd().getQuestion())
            .ip(ctx.getIp())
            .build());

        // 上下文记忆
        LinkedList<ChatMessage> messages = new LinkedList<>();
        messages.addFirst(new SystemMessage(sysPrompt));
        messages.addAll(filterMemoryTurns(ctx.getHistory(), 6));
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        return ChatCompletionRequest.builder()
            .model(volcEngineArkProperties.getMoonshotV132k())
            .messages(messages)
            .build();
    }

}
