package com.dangbei.aisearch.app.executor.query;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.cola.common.enums.YesOrNo;
import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.SharedKnowledgeDocCo;
import com.dangbei.aisearch.client.dto.cmd.query.SharedKnowledgeDocListQuery;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.common.enums.DocProcessStatusEnum;
import com.dangbei.aisearch.common.util.TimeUtil;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.common.util.PageUtil;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.SharedKnowledgeDocDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.SharedKnowledgeDocMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 共享知识库文档列表查询执行器
 *
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Slf4j
@Component
public class SharedKnowledgeDocListQueryExe {

    @Resource
    private SharedKnowledgeDocMapper sharedKnowledgeDocMapper;
    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;

    public PageSingleResponse<SharedKnowledgeDocCo> execute(SharedKnowledgeDocListQuery query) {
        var knowledgeId = query.getKnowledgeId();
        // 获取知识库并校验权限
        SharedKnowledgeEntity knowledgeEntity = getAndValidateKnowledge(knowledgeId);
        // 获取当前用户ID
        String currentUserId = UserContextUtil.getUserId();
        Assert.isTrue(knowledgeEntity.canVisit(currentUserId), "当前用户无权访问知识库");
        // 如果知识库需要审批加入，则需要登录
        if (YesOrNo.isYes(knowledgeEntity.getJoinApprovalRequired())) {
            StpUtil.checkLogin();
        }

        // 构建分页查询
        Page<SharedKnowledgeDocDO> page = new Page<>(query.getPageIndex(), query.getPageSize());
        LambdaQueryWrapper<SharedKnowledgeDocDO> queryWrapper = Wrappers.lambdaQuery(SharedKnowledgeDocDO.class);
        // 过滤条件
        queryWrapper.eq(SharedKnowledgeDocDO::getKnowledgeId, knowledgeId);
        queryWrapper.like(StringUtils.isNotBlank(query.getDocName()), SharedKnowledgeDocDO::getDocName, query.getDocName());

        // 处理中、处理失败、内容不合法的文档只有上传人可见
        queryWrapper.and(wrapper -> {
            // 不在特殊状态列表中的文档，所有人可见
            wrapper.notIn(SharedKnowledgeDocDO::getProcessStatus, DocProcessStatusEnum.notAvailableCode())
                // 或者是特殊状态的文档，但当前用户是上传人
                .or(w -> w.in(SharedKnowledgeDocDO::getProcessStatus, DocProcessStatusEnum.notAvailableCode())
                    .eq(StringUtils.isNotBlank(currentUserId), SharedKnowledgeDocDO::getUploadUserId, currentUserId));
        });

        // 处理状态过滤
        Optional.ofNullable(query.getProcessStatus())
            .ifPresent(status -> queryWrapper.eq(SharedKnowledgeDocDO::getProcessStatus, status));
        // 按更新时间倒序
        queryWrapper.orderByDesc(SharedKnowledgeDocDO::getUpdateTime);
        // 执行分页查询
        Page<SharedKnowledgeDocDO> pageResult = sharedKnowledgeDocMapper.selectPage(page, queryWrapper);
        // 转换分页结果
        return PageUtil.convertToPageSingleResponse(pageResult, docDO -> convertToDocCo(docDO, currentUserId));
    }

    private SharedKnowledgeEntity getAndValidateKnowledge(String knowledgeId) {
        SharedKnowledgeEntity entity = sharedKnowledgeGateway.getByKnowledgeId(knowledgeId);
        Assert.notNull(entity, "知识库不存在");
        return entity;
    }

    private SharedKnowledgeDocCo convertToDocCo(SharedKnowledgeDocDO docDO, String currentUserId) {
        return SharedKnowledgeDocCo.builder()
            .docId(docDO.getDocId())
            .docName(docDO.getDocName())
            .docType(docDO.getDocType())
            .wordNum(docDO.getWordNum())
            .docSize(docDO.getDocSize())
            .processStatus(docDO.getProcessStatus())
            .createTime(TimeUtil.getTimestamp(docDO.getCreateTime()))
            .summary(docDO.getSummary())
            .canDelete(checkCanDelete(docDO, currentUserId))
            .canRename(checkCanRename(docDO, currentUserId))
            .canDownload(checkCanDownload(docDO, currentUserId))
            .build();
    }

    private Boolean checkCanDelete(SharedKnowledgeDocDO docDO, String currentUserId) {
        if (StringUtils.isBlank(currentUserId)) {
            return false;
        }
        // 获取知识库实体
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(docDO.getKnowledgeId());
        if (Objects.isNull(knowledgeEntity)) {
            return false;
        }
        // 检查删除权限
        return knowledgeEntity.checkPermission(currentUserId, SharedKnowledgePermissionEnum.DELETE_DOC);
    }

    private Boolean checkCanRename(SharedKnowledgeDocDO docDO, String currentUserId) {
        if (StringUtils.isBlank(currentUserId)) {
            return false;
        }
        // 获取知识库实体
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(docDO.getKnowledgeId());
        if (Objects.isNull(knowledgeEntity)) {
            return false;
        }
        // 检查重命名权限
        return knowledgeEntity.checkPermission(currentUserId, SharedKnowledgePermissionEnum.RENAME_DOC);
    }

    private Boolean checkCanDownload(SharedKnowledgeDocDO docDO, String currentUserId) {
        if (StringUtils.isBlank(currentUserId)) {
            return false;
        }
        // 获取知识库实体
        SharedKnowledgeEntity knowledgeEntity = sharedKnowledgeGateway.getByKnowledgeId(docDO.getKnowledgeId());
        if (Objects.isNull(knowledgeEntity)) {
            return false;
        }
        // 检查下载权限
        return knowledgeEntity.checkPermission(currentUserId, SharedKnowledgePermissionEnum.DOWNLOAD_DOC);
    }
}
