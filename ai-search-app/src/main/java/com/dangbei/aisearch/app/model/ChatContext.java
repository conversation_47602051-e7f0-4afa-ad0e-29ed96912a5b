package com.dangbei.aisearch.app.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.DTO;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.coze.openapi.client.chat.model.ChatEventType;
import com.coze.openapi.client.connversations.message.model.MessageRole;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.dangbei.aisearch.app.executor.AsyncStoreChatMsgCmdExe;
import com.dangbei.aisearch.app.executor.AsyncUpdateAttachmentCmdExe;
import com.dangbei.aisearch.app.executor.SuggestCmdExe;
import com.dangbei.aisearch.app.util.RequestThreadLocalUtil;
import com.dangbei.aisearch.client.dto.HeaderDTO;
import com.dangbei.aisearch.client.dto.clientobject.DbCard;
import com.dangbei.aisearch.client.dto.clientobject.DbCard2WebSearchCo;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.client.enums.AppTypeEnum;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.common.enums.ChatTypeEnum;
import com.dangbei.aisearch.common.enums.I18nValueEnum;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.common.enums.ProviderEnum;
import com.dangbei.aisearch.common.enums.RoleEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.infrastructure.i18n.I18nUtil;
import com.dangbei.aisearch.infrastructure.knowledge.KnowledgeResponse;
import com.dangbei.aisearch.infrastructure.prompt.RagResponse;
import com.dangbei.aisearch.infrastructure.search.SearchResponse;
import com.dangbei.aisearch.infrastructure.tts.TTSSynthesizer;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import io.jsonwebtoken.lang.Assert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.MDC;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.alibaba.cola.common.constant.RequestConstant.REQUEST_ID;
import static com.dangbei.aisearch.common.constant.CommonConst.Knowledge.ATTACHMENT_PREFIX;
import static com.dangbei.aisearch.common.constant.CommonConst.Knowledge.SHARE_DOC_PREFIX;

/**
 * Chat上下文
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-08
 */
@Slf4j
@Data
public class ChatContext extends DTO {

    // 默认搜索 card 展示的小段摘要的长度
    private static final int DEFAULT_SEARCH_SUMMARY_LENGTH = 35;

    @Schema(description = "SSE流式对象")
    private SseEmitter emitter;

    @Schema(description = "前端聊天入参")
    private ChatCmd chatCmd;

    @Schema(description = "最近的聊天记录")
    private LinkedList<ChatMessageEntity> history;

    @Schema(description = "模型回答实体")
    private MessageModel messageModel = new MessageModel();

    @Schema(description = "问题消息ID")
    private String questionMsgId;

    @Schema(description = "回答消息ID")
    private String answerMsgId;

    @Schema(description = "用户公网IP")
    private String ip;

    @Schema(description = "请求失败信息")
    private String failMsg;

    @Schema(description = "回答是否支持下载")
    private Boolean supportDownload = Boolean.FALSE;

    @Schema(description = "是否开启推荐问题")
    private Boolean enableFollowUp = Boolean.TRUE;

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "资源使用信息")
    private TokenUsageCo usageInfo;

    @Schema(description = "聊天类型(common:普通聊天;quick:快捷会话)")
    private String chatType = ChatTypeEnum.COMMON.getType();

    @Schema(description = "TTS合成器")
    private TTSSynthesizer ttsSynthesizer;

    private transient SuggestCmdExe suggestCmdExe = SpringUtil.getBean(SuggestCmdExe.class);
    private transient AsyncStoreChatMsgCmdExe asyncStoreChatMsgCmdExe = SpringUtil.getBean(AsyncStoreChatMsgCmdExe.class);
    private transient AsyncUpdateAttachmentCmdExe asyncUpdateAttachmentCmdExe = SpringUtil.getBean(AsyncUpdateAttachmentCmdExe.class);

    public ChatContext(SseEmitter emitter, ChatCmd chatCmd, LinkedList<ChatMessageEntity> history) {
        this.emitter = emitter;
        this.chatCmd = chatCmd;
        this.history = history;
    }

    public ChatContext(SseEmitter emitter, ChatCmd chatCmd, LinkedList<ChatMessageEntity> history, String ip) {
        this.emitter = emitter;
        this.chatCmd = chatCmd;
        this.history = history;
        this.ip = ip;
    }

    /**
     * 拼接回答
     * @param answer 增量回答
     */
    public void appendAnswer(String answer) {
        this.messageModel.getFullMsg().append(answer);
    }

    /**
     * 拼接思考内容
     * @param think 思考内容
     */
    public void appendThink(String think) {
        this.messageModel.getThinkMsg().append(think);
    }

    /**
     * 获取模型完整回答
     * @return 模型回答
     */
    public String getFullMsg() {
        return messageModel.getFullMsg().toString();
    }

    /**
     * 获取会话ID
     * @return 会话ID
     */
    public String getConversationId() {
        return chatCmd.getConversationId();
    }

    /**
     * 获取附件
     * @return 会话ID
     */
    public List<ChatCmd.FileItem> getAttachments() {
        List<ChatCmd.FileItem> files = new ArrayList<>();
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            files = new ArrayList<>(chatCmd.getFiles().stream().filter(file -> file.getFileId().startsWith(ATTACHMENT_PREFIX)).toList());
        }
        var reference = chatCmd.getReference();
        if (CollUtil.isEmpty(reference)) {
            return files;
        }
        // 引用的也加上
        for (ChatCmd.ReferenceItem referenceItem : reference) {
            if (referenceItem.getFileId().startsWith(ATTACHMENT_PREFIX)) {
                var referenceFileItem = new ChatCmd.FileItem();
                referenceFileItem.setFileName(referenceItem.getFileName());
                referenceFileItem.setFileType(referenceItem.getFileType());
                referenceFileItem.setFileUrl(referenceItem.getFileUrl());
                referenceFileItem.setFileId(referenceItem.getFileId());
                referenceFileItem.setFileSize(referenceItem.getFileSize());
                referenceFileItem.setType(referenceItem.getType());
                files.add(referenceFileItem);
            }
        }
       return files;
    }

    /**
     * 获取推荐问题列表
     * @return 推荐问题
     */
    public List<String> getFollowUp() {
        return this.messageModel.getFollowUp();
    }

    /**
     * 获取搜索来源（卡片格式）
     * @return 搜索来源（卡片格式）
     */
    public DbCard getSearchReferences() {
        return this.messageModel.getSearchReferences();
    }

    /**
     * 获取搜索来源（卡片格式）
     * @return 搜索来源卡片JSON字符串
     */
    public String getSearchReferencesAsJsonString() {
        DbCard searchReferences = this.messageModel.getSearchReferences();
        return Objects.isNull(searchReferences) ? null : JSON.toJSONString(searchReferences);
    }

    /**
     * 设置搜索来源（卡片格式）
     * @param dbCard 搜索来源（卡片格式）
     */
    public void setSearchReferences(DbCard dbCard) {
        this.messageModel.setSearchReferences(dbCard);
    }

    /**
     * 获取思考内容
     * @return 思考内容
     */
    public String getThinkMsg() {
        return this.messageModel.getThinkMsg().toString();
    }

    /**
     * 添加推荐问题
     * @param followUp 推荐问题
     */
    public void addFollowUp(List<String> followUp) {
        this.messageModel.setFollowUp(followUp);
    }

    /**
     * 设置外部requestId
     * @param requestIdExt 外部调用的requestId
     */
    public void setRequestIdExt(String requestIdExt) {
        if (StrUtil.isNotBlank(requestIdExt)) {
            this.messageModel.setRequestIdExt(requestIdExt);
        }
    }

    /**
     * 获取外部requestId
     * @return 外部requestId
     */
    public String getRequestIdExt() {
        return messageModel.getRequestIdExt();
    }

    /**
     * 发送流式消息
     * @param name SSE名称
     * @param data SSE数据
     * @throws IOException IO异常
     */
    public void sendSSE(String name, Object data) throws IOException {
        emitter.send(SseEmitter.event().name(name).data(data));
    }

    /**
     * 发送联网卡片结果流式消息
     * @param ragResponse 搜索结果
     */
    @SneakyThrows
    public void sendSearchCardSSE(RagResponse ragResponse) {
        // 只给前端发送一次
        if (Objects.isNull(getSearchReferences())) {
            HeaderDTO header = RequestThreadLocalUtil.getHeaderDTO();
            AppTypeEnum appTypeEnum = Optional.ofNullable(header)
                .map(HeaderDTO::getAppType)
                .orElse(null);
            boolean isSmallScreen = Objects.nonNull(appTypeEnum) && appTypeEnum.isSmallScreen();

            // 文案信息，left=长文案，right=短文案
            ImmutablePair<String, String> pair = cardShowText(ragResponse);

            MessageModel.Msg notice = newSseMsg(MsgContentTypeEnum.PROGRESS, isSmallScreen ? pair.getRight() : pair.getLeft());
            this.sendDeltaAnswerSSE(notice);

            DbCard2WebSearchCo searchReferences = buildDbCard2(ragResponse);
            searchReferences.setTitle(pair.getLeft());
            searchReferences.setShortTitle(pair.getRight());
            MessageModel.Msg webCard = newSseMsg(MsgContentTypeEnum.CARD, ObjectMapperUtil.toJson(searchReferences));
            this.sendDeltaAnswerSSE(webCard);

            // 保存新闻卡片至EXT，用于聊天记录回显
            this.setSearchReferences(searchReferences);
        }
    }

    /**
     * 卡片展示文案
     * @param ragResponse 卡片结果
     * @return left=WEB端文案，right=APP端文案
     */
    private ImmutablePair<String, String> cardShowText(RagResponse ragResponse) {
        boolean hasWebResult = false;
        boolean hasKnowledgeResult = false;

        int webActNum = 0;
        String webShowNum = null;
        int knowledgeNum = 0;
        if (ragResponse.isSearchNotEmpty()) {
            hasWebResult = true;
            webActNum = ragResponse.getSearchResponse().getSize();
            webShowNum = NumberUtil.decimalFormat(",###", generateWebNum(webActNum));
        }

        if (ragResponse.isKnowledgeNotEmpty()) {
            hasKnowledgeResult = true;
            knowledgeNum = CollUtil.size(ragResponse.getKnowledgeResponse().getDocList());
        }

        String longText = StrUtil.EMPTY;
        String shortText = StrUtil.EMPTY;
        if (hasWebResult && hasKnowledgeResult) {
            longText = I18nUtil.get(I18nValueEnum.SEARCH_CARD_TITLE_WEB_KNOWLEDGE, new Object[]{knowledgeNum, webShowNum, webActNum});
            shortText = I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_WEB_KNOWLEDGE, new Object[]{knowledgeNum, webActNum});

        } else if (hasWebResult) {
            longText = I18nUtil.get(I18nValueEnum.SEARCH_CARD_TITLE_WEB, new Object[]{webShowNum, webActNum});
            shortText = I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_WEB, new Object[]{webActNum});

        } else if (hasKnowledgeResult) {
            longText = I18nUtil.get(I18nValueEnum.SEARCH_CARD_TITLE_KNOWLEDGE, new Object[]{knowledgeNum});
            shortText = I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_KNOWLEDGE, new Object[]{knowledgeNum});

        }

        return new ImmutablePair<>(longText, shortText);
    }

    /**
     * 发送会话聊天完成SSE流式消息
     * @throws IOException IO异常
     */
    public void sendChatCompletedSSE() throws IOException {
        MessageModel.Msg msg = new MessageModel.Msg();
        msg.setConversationId(getConversationId());
        msg.setId(this.answerMsgId);
        msg.setParentMsgId(questionMsgId);
        msg.setSupportDownload(this.supportDownload);
        this.sendSSE(ChatEventType.CONVERSATION_CHAT_COMPLETED.getValue(), ObjectMapperUtil.toJson(msg));
        log.info("sendChatCompletedSSE发送完成");
    }

    /**
     * 发送增量回答流式消息
     * @param content 回答
     * @throws IOException IO异常
     */
    public void sendDeltaAnswerSSE(String content) throws IOException {
        MessageModel.Msg msg = newSseMsg(MsgContentTypeEnum.TEXT, content);
        this.sendSSE(ChatEventType.CONVERSATION_MESSAGE_DELTA.getValue(), ObjectMapperUtil.toJson(msg));
    }

    /**
     * 发送增量思考流式消息
     * @param content 回答
     * @throws IOException IO异常
     */
    public void sendDeltaThinkSSE(String content) throws IOException {
        MessageModel.Msg msg = newSseMsg(MsgContentTypeEnum.THINKING, content);
        this.sendSSE(ChatEventType.CONVERSATION_MESSAGE_DELTA.getValue(), ObjectMapperUtil.toJson(msg));
    }

    /**
     * 发送增量回答流式消息
     * @param msg 消息对象
     * @throws IOException IO异常
     */
    public void sendDeltaAnswerSSE(MessageModel.Msg msg) throws IOException {
        this.sendSSE(ChatEventType.CONVERSATION_MESSAGE_DELTA.getValue(), ObjectMapperUtil.toJson(msg));
    }

    /**
     * 发送推荐问题SSE流式消息
     * @throws IOException IO异常
     */
    public void sendFollowUpSSE(List<String> suggests) throws IOException {
        if (CollUtil.isNotEmpty(suggests)) {
            for (String text : suggests) {
                MessageModel.Msg msg = new MessageModel.Msg();
                msg.setType(MessageType.FOLLOW_UP.getValue());
                msg.setRole(MessageRole.ASSISTANT.getValue());
                msg.setContentType(MsgContentTypeEnum.FOLLOW_UP.getValue());
                msg.setContent(text);
                msg.setSupportDownload(this.supportDownload);
                msg.setRequestId(MDC.get(REQUEST_ID));
                this.sendSSE(ChatEventType.CONVERSATION_MESSAGE_COMPLETED.getValue(), ObjectMapperUtil.toJson(msg));
            }
            log.info("sendFollowUpSSE发送完成");
        }
    }

    /**
     * 发送推荐问题Loading SSE流式消息
     * @throws IOException IO异常
     */
    public void sendFollowUpLoadingSSE() throws IOException {
        this.sendSSE("conversation.followup.loading", new JSONObject());
    }

    /**
     * 发送绿网拦截流式消息
     */
    public void sendGreenInterceptSSE() {
        try {
            this.setFailMsg("涉及敏感话题");
            this.sendDeltaAnswerSSE("尊敬的用户您好，让我们换个话题再聊聊吧！");
            this.getMessageModel().setFullMsg(new StringBuffer("尊敬的用户您好，让我们换个话题再聊聊吧！"));
        } catch (IOException ex) {
            log.debug(ex.getMessage(), ex);
        }
    }

    /**
     * 发送并发请求流式消息
     */
    public void sendConcurrentRequestSSE() {
        try {
            this.setFailMsg("ConcurrentRequest");
            this.sendDeltaAnswerSSE("系统繁忙中，请稍后再试");
            this.getMessageModel().setFullMsg(new StringBuffer("系统繁忙中，请稍后再试"));
        } catch (IOException err) {
            log.debug(err.getMessage(), err);
        }
    }

    /**
     * 创建SSE流式消息对象
     * @param contentType 消息类型
     * @param content     消息内容
     * @return 前端增量消息对象
     */
    public MessageModel.Msg newSseMsg(MsgContentTypeEnum contentType, String content) {
        Assert.notNull(this.answerMsgId, "回答ID为空");
        Assert.notNull(this.questionMsgId, "提问ID为空");
        Assert.notNull(this.getConversationId(), "会话ID为空");

        return new MessageModel.Msg()
            .setId(this.answerMsgId)
            .setType(MessageType.ANSWER.getValue())
            .setRole(RoleEnum.ASSISTANT.getRoleType())
            .setContent(content)
            .setContentType(contentType.getValue())
            .setConversationId(this.getConversationId())
            .setCreatedAt(DateUtil.currentSeconds())
            .setRequestId(MDC.get(REQUEST_ID))
            .setSupportDownload(this.supportDownload)
            .setParentMsgId(this.questionMsgId);
    }

    /**
     * 构建联网搜索卡片
     * @param ragResponse 搜索结果
     * @return 搜索卡片
     */
    private DbCard2WebSearchCo buildDbCard2(RagResponse ragResponse) {
        List<DbCard2WebSearchCo.CardItem> cardItems = new ArrayList<>();
        if (ragResponse.isKnowledgeNotEmpty()) {
            // 知识库数量
            List<KnowledgeResponse.DocResult> docList = ragResponse.getKnowledgeResponse().getDocList();
            List<DbCard2WebSearchCo.KnowledgeInfo> cardList = new ArrayList<>();
            for (int i = 0; i < docList.size(); i++) {
                KnowledgeResponse.DocResult docResult = docList.get(i);
                DbCard2WebSearchCo.KnowledgeInfo knowledgeInfo = new DbCard2WebSearchCo.KnowledgeInfo()
                    .setIdIndex("doc_" + (i + 1))
                    .setDocId(docResult.getDocId())
                    .setDocName(docResult.getDocName())
                    .setDocType(docResult.getDocType())
                    .setDocSize(docResult.getDocSize())
                    .setWordNum(docResult.getWordNum());
                cardList.add(knowledgeInfo);
            }

            DbCard2WebSearchCo.CardItem step = new DbCard2WebSearchCo.CardItem()
                .setName(I18nUtil.get(I18nValueEnum.SEARCH_CARD_TITLE_KNOWLEDGE, new Object[]{cardList.size()}))
                .setShortName(I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_KNOWLEDGE, new Object[]{cardList.size()}))
                .setType("2003")
                .setContent(JSON.toJSONString(cardList));
            cardItems.add(step);
        }

        if (ragResponse.isSearchNotEmpty()) {
            // 联网结果
            SearchResponse searchResp = ragResponse.getSearchResponse();
            List<DbCard2WebSearchCo.WebInfo> cradItemList = searchResp.getSearchResults()
                .stream()
                .map(i -> new DbCard2WebSearchCo.WebInfo()
                    .setIdIndex(i.getIdIndex())
                    .setUrl(i.getUrl())
                    .setThumbnailUrl(i.getIcon())
                    .setSiteName(i.getSiteName())
                    .setName(i.getTitle())
                    .setSnippet(StrUtil.sub(i.getSummary(), 0, DEFAULT_SEARCH_SUMMARY_LENGTH))
                ).toList();

            DbCard2WebSearchCo.CardItem step1 = new DbCard2WebSearchCo.CardItem()
                .setName(I18nUtil.get(I18nValueEnum.CHAT_SEARCH_CARD_PROGRESS_1))
                .setType("2001")
                .setContent(JSON.toJSONString(searchResp.getQuery()));
            DbCard2WebSearchCo.CardItem step2 = new DbCard2WebSearchCo.CardItem()
                .setName(I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_WEB, new Object[]{cradItemList.size()}))
                .setShortName(I18nUtil.get(I18nValueEnum.H5_SEARCH_CARD_TITLE_WEB, new Object[]{cradItemList.size()}))
                .setType("2002")
                .setContent(JSON.toJSONString(cradItemList));
            cardItems.add(step1);
            cardItems.add(step2);
        }

        DbCard2WebSearchCo.CardInfo cardInfo = new DbCard2WebSearchCo.CardInfo()
            .setInitTitle(I18nUtil.get(I18nValueEnum.CHAT_SEARCH_CARD_PROGRESS_2))
            .setCardItems(cardItems);
        return new DbCard2WebSearchCo()
            .setCardType("DB-CARD-2")
            .setCardInfo(cardInfo);
    }

    /**
     * 异步保存聊天记录
     */
    public void asyncStoreChatMsg() {
        asyncStoreChatMsgCmdExe.execute(this);
    }

    /**
     * 异步更新附件会话ID
     */
    public void asyncStoreAttachmentConversationId() {
        if (carryAttachments()) {
            asyncUpdateAttachmentCmdExe.execute(this.getChatCmd().getFiles(), this.getConversationId());
        }
    }

    /**
     * 生成推荐问题
     * @return 推荐问题
     */
    public List<String> generateFollowUp() {
        // 当前用户提问
        ChatMessageEntity userMsg = new ChatMessageEntity();
        userMsg.setRole(RoleEnum.USER.getRoleType());
        userMsg.setContent(chatCmd.getQuestion());

        // 当前模型回答
        ChatMessageEntity aiMsg = new ChatMessageEntity();
        aiMsg.setRole(RoleEnum.ASSISTANT.getRoleType());
        aiMsg.setContent(this.getFullMsg());

        // 添加进回答
        if (Objects.nonNull(this.history)) {
            this.history.addFirst(userMsg);
            this.history.addFirst(aiMsg);
        }
        return suggestCmdExe.execute(this.history);
    }

    /**
     * 是否开启推荐问题
     * @return 是否开启推荐问题
     */
    public boolean isEnableSuggest() {
        return Objects.nonNull(this.enableFollowUp) && this.enableFollowUp;
    }

    /**
     * 统计token消耗
     * @param resp     模型增量回答
     * @param answerAt 回答开始时间
     */
    @Deprecated
    public void collectUsage(ChatCompletionResult resp, long answerAt) {
        if (Objects.isNull(usageInfo)) {
            usageInfo = new TokenUsageCo(resp.getLlmProvider(), resp.getModel());
        }

        // 模型返回的token用量，实时记录
        if (Objects.nonNull(resp.getUsage())) {
            usageInfo.setInputTokens(Convert.toLong(resp.getUsage().getPromptTokens()));
            usageInfo.setOutputTokens(Convert.toLong(resp.getUsage().getCompletionTokens()));
            usageInfo.setTotalTokens(Convert.toLong(resp.getUsage().getTotalTokens()));
        }

        // 模型供应商补充记录
        if (StrUtil.isBlank(usageInfo.getProvider())) {
            usageInfo.setProvider(resp.getLlmProvider());
        }
        if (StrUtil.isBlank(usageInfo.getModel())) {
            usageInfo.setModel(resp.getModel());
        }

        // record
        usageInfo.setCostTime(Convert.toInt(System.currentTimeMillis() - answerAt) / 1000);
    }

    /**
     * 统计token消耗
     * @param resp     模型增量回答
     * @param answerAt 回答开始时间
     */
    public void collectUsageV2(com.dangbei.framework.insight.volcopenai.model.completion.chat.ChatCompletionChunk resp, long answerAt) {
        if (Objects.isNull(usageInfo)) {
            usageInfo = new TokenUsageCo(ProviderEnum.matchProviderCode(resp.getModel()), resp.getModel());
        }

        // 模型返回的token用量，实时记录
        if (Objects.nonNull(resp.getUsage())) {
            usageInfo.setInputTokens(Convert.toLong(resp.getUsage().getPromptTokens()));
            usageInfo.setOutputTokens(Convert.toLong(resp.getUsage().getCompletionTokens()));
            usageInfo.setTotalTokens(Convert.toLong(resp.getUsage().getTotalTokens()));
        }

        // TODO 模型供应商补充记录
        if (StrUtil.isBlank(usageInfo.getProvider())) {
            usageInfo.setProvider(StrUtil.EMPTY);
        }
        if (StrUtil.isBlank(usageInfo.getModel())) {
            usageInfo.setModel(resp.getModel());
        }

        // record
        usageInfo.setCostTime(Convert.toInt(System.currentTimeMillis() - answerAt) / 1000);
    }

    /**
     * 记录首token耗时
     * @param startAt 开始时间
     */
    public void recordFirstTokenCost(long startAt) {
        long firstTokenCostMs = System.currentTimeMillis() - startAt;
        if (Objects.isNull(usageInfo)) {
            usageInfo = new TokenUsageCo();
        }
        usageInfo.setFirstTokenCostMs(Convert.toInt(firstTokenCostMs));
        log.info("首token耗时：{}ms", firstTokenCostMs);
    }

    private int generateWebNum(int actNum) {
        // 根据Y的值生成一个基础值，这里使用Y * 1000作为基础
        int baseValue = actNum * 1000;

        // 生成一个随机增量，范围在1000到10000之间
        int randomIncrement = RandomUtil.randomInt(1000, 10000);

        // 计算新的X值，确保不超过50000
        int newX = actNum + baseValue + randomIncrement;
        if (newX > 50000) {
            newX = RandomUtil.randomInt(3000, 50000);
        }
        return newX;
    }

    /**
     * 是否需要检索知识库
     * @return true=是，false=否
     */
    public boolean needSearchKnowledge() {
        long fileCount = 0;
        long referCount = 0;
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            fileCount = chatCmd.getFiles().stream()
                .filter(i -> (StrUtil.startWith(i.getFileId(), CommonConst.Knowledge.DOC_PREFIX) || StrUtil.startWith(i.getFileId(), SHARE_DOC_PREFIX)))
                .count();
        }
        if (CollUtil.isNotEmpty(chatCmd.getReference())) {
            referCount = chatCmd.getReference().stream()
                .filter(i -> StrUtil.startWith(i.getFileId(), CommonConst.Knowledge.DOC_PREFIX))
                .count();
        }

        if (CollUtil.isNotEmpty(chatCmd.getKnowledgeList())) {
            referCount = chatCmd.getKnowledgeList().size();
        }

        return (Objects.nonNull(chatCmd.getChatOption()) && (chatCmd.getChatOption().isSearchKnowledge() || chatCmd.getChatOption().isSearchAllKnowledge()))
            || ((fileCount + referCount) > 0);
    }

    /**
     * 是否需要检索附件
     * @return true=是，false=否
     */
    public boolean needSearchAttachment() {
        long fileCount = 0;
        long referCount = 0;
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            fileCount = chatCmd.getFiles().stream()
                .filter(i -> StrUtil.startWith(i.getFileId(), ATTACHMENT_PREFIX))
                .count();
        }
        if (CollUtil.isNotEmpty(chatCmd.getReference())) {
            referCount = chatCmd.getReference().stream()
                .filter(i -> StrUtil.startWith(i.getFileId(), ATTACHMENT_PREFIX))
                .count();
        }
        return (fileCount + referCount) > 0;
    }


    /**
     * 本次是否携带附件
     * @return true=是，false=否
     */
    public boolean carryAttachments() {
        if (CollUtil.isNotEmpty(chatCmd.getFiles())) {
            return chatCmd.getFiles().stream().anyMatch(i -> StrUtil.startWith(i.getFileId(), ATTACHMENT_PREFIX));
        }
        return false;
    }
}
