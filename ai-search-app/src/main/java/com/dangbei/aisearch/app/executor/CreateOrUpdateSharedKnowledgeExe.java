package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.cmd.SaveSharedKnowledgeCmd;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.config.properties.ContentSecurityProperties;
import com.dangbei.aisearch.infrastructure.config.properties.ShareKnowledgeProperties;
import com.dangbei.devbase.client.enums.GreenTextServiceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 创建或更新共享知识库执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class CreateOrUpdateSharedKnowledgeExe {

    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private ExternalCommonGateway externalCommonGateway;
    @Resource
    private ContentSecurityProperties contentSecurityProperties;
    @Resource(name = "contentSecurityExecutor")
    private Executor contentSecurityExecutor;
    @Resource
    private ShareKnowledgeProperties shareKnowledgeProperties;

    /**
     * 执行创建或更新共享知识库
     * @param cmd 保存命令
     * @return 响应结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Response execute(SaveSharedKnowledgeCmd cmd) {
        // 获取当前用户
        String currentUserId = UserContextUtil.getNonNullUserId();
        if (cmd.isUpdate()) {
            return executeUpdate(cmd, currentUserId);
        } else {
            return executeCreate(cmd, currentUserId);
        }
    }

    /**
     * 执行创建操作
     * @param cmd 创建命令
     * @param currentUserId 当前用户ID
     * @return 响应结果
     */
    private Response executeCreate(SaveSharedKnowledgeCmd cmd, String currentUserId) {
        // 参数校验
        validateCreateParams(cmd);
        // 内容安全检测
        performContentSafetyCheck(cmd);
        // 创建知识库实体
        SharedKnowledgeEntity knowledgeEntity = createKnowledgeEntity(cmd, currentUserId);
        // 保存知识库
        knowledgeEntity.save();
        // 添加创建人为成员
        addCreatorAsMember(knowledgeEntity, currentUserId);
        return Response.buildSuccess();
    }

    /**
     * 执行更新操作
     * @param cmd 更新命令
     * @param currentUserId 当前用户ID
     * @return 响应结果
     */
    private Response executeUpdate(SaveSharedKnowledgeCmd cmd, String currentUserId) {
        // 参数校验
        validateUpdateParams(cmd);
        // 查询知识库实体
        SharedKnowledgeEntity knowledgeEntity = getKnowledgeEntity(cmd.getId());
        // 权限校验
        knowledgeEntity.validatePermission(currentUserId, SharedKnowledgePermissionEnum.EDIT_KNOWLEDGE);
        // 内容安全检测
        performContentSafetyCheck(cmd);
        // 更新知识库信息
        updateKnowledgeEntity(knowledgeEntity, cmd);
        return Response.buildSuccess();
    }

    /**
     * 验证创建参数
     * @param cmd 创建命令
     */
    private void validateCreateParams(SaveSharedKnowledgeCmd cmd) {
        Assert.notBlank(cmd.getName(), "知识库名称不能为空");
        Assert.isTrue(cmd.getName().length() <= 50, "知识库名称长度不能超过50个字符");

        if (StringUtils.isNotBlank(cmd.getDescription())) {
            Assert.isTrue(cmd.getDescription().length() <= 200, "知识库描述长度不能超过200个字符");
        }
    }

    /**
     * 验证更新参数
     * @param cmd 更新命令
     */
    private void validateUpdateParams(SaveSharedKnowledgeCmd cmd) {
        Assert.notNull(cmd.getId(), "知识库ID不能为空");
        if (StringUtils.isNotBlank(cmd.getDescription())) {
            Assert.isTrue(cmd.getDescription().length() <= 200, "知识库描述长度不能超过200个字符");
        }
    }

    /**
     * 获取知识库实体
     * @param id 知识库主键ID
     * @return 知识库实体
     */
    private SharedKnowledgeEntity getKnowledgeEntity(Long id) {
        SharedKnowledgeEntity entity = sharedKnowledgeGateway.loadById(id);
        if (Objects.isNull(entity)) {
            throw new BizException("知识库不存在");
        }
        return entity;
    }

    /**
     * 内容安全检测
     * @param cmd 命令
     */
    private void performContentSafetyCheck(SaveSharedKnowledgeCmd cmd) {
        List<CompletableFuture<Void>> checkTasks = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        String knowledgeName = cmd.getName();

        try {
            // 添加名称检测任务
            checkTasks.add(CompletableFuture.supplyAsync(() ->
                externalCommonGateway.textGreenCheck(
                    cmd.getName(),
                    contentSecurityProperties.getNicknameConfidenceScore(),
                    GreenTextServiceEnum.COMMENT_DETECTION_PRO
                ), contentSecurityExecutor)
                .thenAccept(resp -> validateCheckResult(resp, "知识库名称包含不当内容"))
            );

            // 添加描述检测任务（如果有）
            if (StringUtils.isNotBlank(cmd.getDescription())) {
                checkTasks.add(CompletableFuture.supplyAsync(() ->
                    externalCommonGateway.textGreenCheck(
                        cmd.getDescription(), 85F, GreenTextServiceEnum.COMMENT_DETECTION_PRO
                    ), contentSecurityExecutor)
                    .thenAccept(resp -> validateCheckResult(resp, "知识库描述包含不当内容"))
                );
            }

            // 添加图片检测任务（如果有）
            if (StringUtils.isNotBlank(cmd.getCoverUrl())) {
                checkTasks.add(CompletableFuture.supplyAsync(() ->
                    externalCommonGateway.imageGreenCheck(
                        cmd.getCoverUrl(),
                        contentSecurityProperties.getAttachmentImageConfidenceScore()
                    ), contentSecurityExecutor)
                    .thenAccept(resp -> validateCheckResult(resp, "知识库封面图片不可用"))
                );
            }

            // 等待所有检测完成，设置10秒超时
            CompletableFuture.allOf(checkTasks.toArray(new CompletableFuture[0]))
                .get(10, TimeUnit.SECONDS);

            long costTime = System.currentTimeMillis() - startTime;
            log.info("内容安全检测完成 - 知识库[{}], 任务数量:{}, 总耗时:{}ms",
                knowledgeName, checkTasks.size(), costTime);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("内容安全检测被中断 - 知识库[{}], 耗时:{}ms",
                knowledgeName, System.currentTimeMillis() - startTime);
        } catch (ExecutionException e) {
            // 提取并重新抛出业务异常
            Throwable cause = e.getCause();
            if (cause instanceof CompletionException && cause.getCause() instanceof BizException) {
                log.warn("内容安全检测未通过 - 知识库[{}], 耗时:{}ms, 原因:{}",
                    knowledgeName, System.currentTimeMillis() - startTime, cause.getCause().getMessage());
                throw (BizException) cause.getCause();
            }
            log.warn("内容安全检测异常 - 知识库[{}], 耗时:{}ms, 原因:{}",
                knowledgeName, System.currentTimeMillis() - startTime, e.getMessage());
        } catch (Exception e) {
            log.warn("内容安全检测失败 - 知识库[{}], 耗时:{}ms, 原因:{}",
                knowledgeName, System.currentTimeMillis() - startTime, e.getMessage());
        }
    }

    /**
     * 验证内容安全检测结果
     */
    private void validateCheckResult(Object resp, String errorMsg) {
        if (resp instanceof com.alibaba.cola.dto.SingleResponse<?> response) {
            if (response.isSuccess() &&
                Objects.nonNull(response.getData()) &&
                response.getData() instanceof com.dangbei.devbase.client.dto.GreenCheckResp checkResp &&
                !checkResp.isCheckResult()) {
                throw new CompletionException(new BizException(errorMsg));
            }
        }
    }

    /**
     * 创建知识库实体
     * @param cmd 创建命令
     * @param currentUserId 当前用户ID
     * @return 知识库实体
     */
    private SharedKnowledgeEntity createKnowledgeEntity(SaveSharedKnowledgeCmd cmd, String currentUserId) {
        SharedKnowledgeEntity entity = new SharedKnowledgeEntity();
        // 初始化知识库
        entity.initializeKnowledge(currentUserId, shareKnowledgeProperties.getCollectionName(), shareKnowledgeProperties.getNamespace());
        // 设置基本信息和权限配置
        setEntityProperties(entity, cmd);
        return entity;
    }

    /**
     * 更新知识库实体
     * @param entity 知识库实体
     * @param cmd 更新命令
     */
    private void updateKnowledgeEntity(SharedKnowledgeEntity entity, SaveSharedKnowledgeCmd cmd) {
        // 设置基本信息和权限配置
        setEntityProperties(entity, cmd);
        entity.update();
    }

    /**
     * 设置实体属性
     * @param entity 知识库实体
     * @param cmd 命令
     */
    private void setEntityProperties(SharedKnowledgeEntity entity, SaveSharedKnowledgeCmd cmd) {
        // 设置基本信息
        entity.setName(cmd.getName());
        entity.setDescription(cmd.getDescription());
        entity.setCoverUrl(cmd.getCoverUrl());
        // 设置权限配置
        entity.setJoinApprovalRequired(Objects.equals(cmd.getJoinApprovalRequired(), Boolean.TRUE) ? 1 : 0);
        entity.setDownloadEnabled(Objects.equals(cmd.getDownloadEnabled(), Boolean.TRUE) ? 1 : 0);
    }

    /**
     * 添加创建人为成员
     * @param knowledgeEntity 知识库实体
     * @param currentUserId 当前用户ID
     */
    private void addCreatorAsMember(SharedKnowledgeEntity knowledgeEntity, String currentUserId) {
        knowledgeEntity.addMember(currentUserId, SharedKnowledgeRoleEnum.CREATOR, null);
    }
}
