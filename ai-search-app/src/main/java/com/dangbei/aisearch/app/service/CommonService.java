package com.dangbei.aisearch.app.service;

import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.executor.IpCheckCmdExe;
import com.dangbei.aisearch.app.executor.SendSmsCmdExe;
import com.dangbei.aisearch.app.executor.TencentCaptchaEncryptedAppIdCmdExe;
import com.dangbei.aisearch.client.dto.clientobject.IpCheckCo;
import com.dangbei.aisearch.client.dto.clientobject.SendSmsCo;
import com.dangbei.aisearch.client.dto.cmd.SendSmsCmd;
import com.dangbei.aisearch.client.dto.cmd.TencentCaptchaEncryptedAppIdCmd;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Service
public class CommonService {

    @Resource
    private SendSmsCmdExe sendSmsCmdExe;
    @Resource
    private IpCheckCmdExe ipCheckCmdExe;
    @Resource
    private TencentCaptchaEncryptedAppIdCmdExe tencentCaptchaEncryptedAppIdCmdExe;

    /**
     * 发送短信
     */
    public SingleResponse<SendSmsCo> sendSms(SendSmsCmd sendSmsCmd) {
        return sendSmsCmdExe.execute(sendSmsCmd);
    }

    public SingleResponse<IpCheckCo> ipCheck(List<String> ips) {
        return ipCheckCmdExe.execute(ips);
    }

    /**
     * 获取腾讯云验证码加密的验证码应用ID
     */
    public SingleResponse<String> getTencentCaptchaEncryptedAppId(TencentCaptchaEncryptedAppIdCmd cmd) {
        return tencentCaptchaEncryptedAppIdCmdExe.execute(cmd);
    }
}
