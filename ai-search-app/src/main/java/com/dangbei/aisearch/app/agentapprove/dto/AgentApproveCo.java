package com.dangbei.aisearch.app.agentapprove.dto;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.dto.AgentChatExample;
import com.dangbei.aisearch.client.dto.AgentFollowUp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28 15:16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentApproveCo extends DTO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "智能体唯一ID")
    private String agentId;

    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体简介")
    private String intro;

    @Schema(description = "智能体描述")
    private String description;

    @Schema(description = "智能体图标")
    private String iconUrl;

    @Schema(description = "初始化推荐问题 [{\"type\" 1, \"value\": \"你好\"}]")
    private List<AgentFollowUp> followUp;

    @Schema(description = "系统提示词")
    private String systemPrompt;

    @Schema(description = "APP端背景图")
    private String appBgUrl;

    @Schema(description = "智能体背景主色调")
    private String appBgColorTone;

    @Schema(description = "智能体音色：https://www.volcengine.com/docs/6561/1257544")
    private String voiceType;

    @Schema(description = "展示创建人")
    private String createShowPerson;

    @Schema(description = "用户给智能体设定提示词 用于前端展示")
    private String userPrompt;

    @Schema(description = "智能体性格描述")
    private String personality;

    @Schema(description = "智能体开场白")
    private String greeting;

    @Schema(description = "智能体性别：0-男 1-女 2-非人类角色")
    private Integer gender;

    @Schema(description = "智能体角色类型：0-角色类 1-助理类")
    private Integer role;

    @Schema(description = "智能体来源：0-官方创建 1-用户创建 2-大屏同步")
    private Integer source;

    @Schema(description = "智能体公开状态：0-公开 1-私密 2-部分公开")
    private Integer visibility;

    @Schema(description = "智能体技能 online")
    private List<String> skills;

    @Schema(description = "智能体对话示例")
    private List<AgentChatExample> chatExample;

    @Schema(description = "智能体语音开关 0-关 1-开")
    private Integer voiceEnabled;

    @Schema(description = "上架状态：-1-冻结 0-正常 1-用户删除 2-运营下架")
    private Integer onlineStatus;

    @Schema(description = "创建者用户")
    private String createUserId;

    @Schema(description = "关联的最新版本号")
    private Long latestVersionId;
}
