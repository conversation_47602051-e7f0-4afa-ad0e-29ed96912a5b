//package com.dangbei.aisearch.app.extension;
//
//import com.alibaba.cola.extension.Extension;
//import com.dangbei.aisearch.app.executor.ChatCmdExe;
//import com.dangbei.aisearch.app.extensionpoint.ChatSceneExtPt;
//import com.dangbei.aisearch.common.constant.CommonConst;
//import lombok.extern.slf4j.Slf4j;
//
//import javax.annotation.Resource;
//
///**
// * 图像理解对话扩展点
// * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
// * @version 1.0.0
// * @since 2025-01-15
// */
//@Slf4j
//@Extension(bizId = CommonConst.Intent.LINK_READ)
//public class LinkReadChatExt implements ChatSceneExtPt {
//
//    @Resource
//    private ChatCmdExe chatCmdExe;
//
//    @Override
//    public void streamChat(ChatContext ctx) {
//        ctx.getChatCmd().setBotCode("AI_SEARCH");
//        chatCmdExe.execute(ctx.getChatCmd(), ctx.getEmitter());
//    }
//
//}
