package com.dangbei.aisearch.app.modelchat.dashscope;

import com.alibaba.cola.extension.Extension;
import com.dangbei.aisearch.app.model.ChatContext;
import com.dangbei.aisearch.common.constant.CommonConst;
import com.dangbei.aisearch.infrastructure.config.properties.DashScopeProperties;
import com.dangbei.aisearch.infrastructure.prompt.PromptParam;
import com.dangbei.aisearch.infrastructure.prompt.PromptUtil;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.SystemMessage;
import com.theokanning.openai.completion.chat.UserMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.LinkedList;

/**
 * qwen-plus + 联网
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-24
 */
@Slf4j
@Component("plus_dashscope_model_chat")
@Extension(bizId = CommonConst.ModelIntent.QWEN)
public class Plus extends AbstractDashScopeModelChatExt {

    @Override
    protected ChatCompletionRequest buildParam(ChatContext ctx) {
        // TODO 提示词抽取
        DashScopeProperties.ModelConfig onlineRefSearch = dashScopeProperties.getOnlineSearch();
        String sysPrompt = promptUtil.format(PromptParam.builder()
            .promptTmpl(onlineRefSearch.getSystem())
            .question(ctx.getChatCmd().getQuestion())
            .ip(ctx.getIp())
            .build());

        // 上下文记忆
        LinkedList<ChatMessage> messages = new LinkedList<>();
        messages.addFirst(new SystemMessage(sysPrompt));
        messages.addAll(filterMemoryTurns(ctx.getHistory(), 6));
        messages.add(new UserMessage(ctx.getChatCmd().getQuestion()));

        return ChatCompletionRequest.builder()
            .model("qwen-plus")
            .messages(messages)
            .build();
    }

}
