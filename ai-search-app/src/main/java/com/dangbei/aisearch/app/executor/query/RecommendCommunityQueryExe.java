package com.dangbei.aisearch.app.executor.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.enums.YesOrNo;
import com.alibaba.cola.dto.MultiResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.CommunityDTO;
import com.dangbei.aisearch.client.dto.clientobject.CommunityPostCo;
import com.dangbei.aisearch.domain.entity.CommunityPostEntity;
import com.dangbei.aisearch.domain.entity.CommunityPostReportEntity;
import com.dangbei.aisearch.domain.gateway.CommunityPostGateway;
import com.dangbei.aisearch.domain.gateway.CommunityPostReportGateway;
import com.dangbei.aisearch.infrastructure.convertor.CommunityPostConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.CommunityPostDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.CommunityPostMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * .
 * <p>
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Component
public class RecommendCommunityQueryExe {
    @Resource
    private CommunityPostMapper communityPostMapper;
    @Resource
    private CommunityPostConvertor communityPostConvertor;
    @Resource
    private CommunityPostGateway communityPostGateway;
    @Resource
    private CommunityPostReportGateway communityPostReportGateway;

    public MultiResponse<CommunityPostCo> execute(Integer limit) {
        String userId = UserContextUtil.getUserId();

        Set<String> reportPostId = new HashSet<>();
        if (StrUtil.isNotBlank(userId)) {
            List<CommunityPostReportEntity> communityPostReportEntities = communityPostReportGateway.listByUserId(userId);
            reportPostId = communityPostReportEntities.stream().map(CommunityPostReportEntity::getPostId).collect(Collectors.toSet());
        }

        // 判断精选帖子数量是否满足limit个
        LambdaQueryWrapper<CommunityPostDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CommunityPostDO::getIsRecommend, YesOrNo.YES.getValue());
        wrapper.eq(CommunityPostDO::getOnlineStatus, YesOrNo.YES.getValue());
        wrapper.eq(CommunityPostDO::getIsSeo, YesOrNo.NO.getValue());
        wrapper.notIn(CollUtil.isNotEmpty(reportPostId), CommunityPostDO::getPostId, reportPostId);
        Long count = communityPostMapper.selectCount(wrapper);
        if (count <= limit) {
            // 取所有推荐数据
            LambdaQueryWrapper<CommunityPostDO> wrapperAll = Wrappers.lambdaQuery();
            wrapperAll.eq(CommunityPostDO::getIsRecommend, YesOrNo.YES.getValue());
            wrapperAll.eq(CommunityPostDO::getOnlineStatus, YesOrNo.YES.getValue());
            wrapperAll.eq(CommunityPostDO::getIsSeo, YesOrNo.NO.getValue());
            wrapperAll.notIn(CollUtil.isNotEmpty(reportPostId), CommunityPostDO::getPostId, reportPostId);
            List<CommunityPostEntity> recommendList = communityPostConvertor.toEntityList(communityPostMapper.selectList(wrapperAll));
            return MultiResponse.of(communityPostGateway.getCommunityPost(recommendList, userId));
        }

        // 推荐数据>50个
        CommunityDTO communityDTO = communityPostMapper.getMaxAndMinId();
        if (Objects.isNull(communityDTO)) {
            return MultiResponse.of(new ArrayList<>());
        }
        Long id = RandomUtil.randomLong(communityDTO.getMinId(), communityDTO.getMaxId());

        // 2. 随机取出limit个帖子
        List<CommunityPostDO> communityPostDOList = communityPostList(id, limit, false);

        // 判断是否满足limit个
        if (communityPostDOList.size() < limit) {
            communityPostDOList.addAll(communityPostList(id, limit - communityPostDOList.size(), true));
        }

        if (CollUtil.isEmpty(communityPostDOList)) {
            return MultiResponse.of(new ArrayList<>());
        }

        List<CommunityPostEntity> entityList = communityPostConvertor.toEntityList(communityPostDOList);
        return MultiResponse.of(communityPostGateway.getCommunityPost(entityList, userId));
    }

    /**
     * id为空时，查询全部帖子，不为空时，查询小于id的帖子，complement为true时，查询大于id的帖子，complement为false时，查询小于id的帖子
     * @param id         主键id
     * @param limit      查询数量
     * @param complement 是否补数
     * @return 数据
     */
    private List<CommunityPostDO> communityPostList(Long id, Integer limit, Boolean complement) {
        String userId = UserContextUtil.getUserId();

        Set<String> reportPostId = new HashSet<>();
        if (StrUtil.isNotBlank(userId)) {
            List<CommunityPostReportEntity> communityPostReportEntities = communityPostReportGateway.listByUserId(userId);
            reportPostId = communityPostReportEntities.stream().map(CommunityPostReportEntity::getPostId).collect(Collectors.toSet());
        }

        LambdaQueryWrapper<CommunityPostDO> wrapper = Wrappers.lambdaQuery();
        if (complement) {
            wrapper.le(CommunityPostDO::getId, id);
        } else {
            wrapper.gt(CommunityPostDO::getId, id);
        }

        wrapper.eq(CommunityPostDO::getOnlineStatus, YesOrNo.YES.getValue());
        wrapper.eq(CommunityPostDO::getIsSeo, YesOrNo.NO.getValue());
        wrapper.eq(CommunityPostDO::getIsRecommend, YesOrNo.YES.getValue());
        wrapper.notIn(CollUtil.isNotEmpty(reportPostId), CommunityPostDO::getPostId, reportPostId);
        wrapper.last(String.format("limit %s", limit));
        return communityPostMapper.selectList(wrapper);
    }

}
