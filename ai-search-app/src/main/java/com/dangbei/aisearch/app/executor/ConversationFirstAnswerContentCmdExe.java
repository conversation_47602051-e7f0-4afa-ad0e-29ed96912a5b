package com.dangbei.aisearch.app.executor;

import com.alibaba.cola.dto.Response;
import com.dangbei.aisearch.common.enums.ChatTypeEnum;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.entity.ConversationEntity;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import com.dangbei.aisearch.domain.gateway.ConversationGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-10
 */
@Slf4j
@Component
public class ConversationFirstAnswerContentCmdExe {

    @Resource
    private ChatMessageGateway chatMessageGateway;
    @Resource
    private ConversationGateway conversationGateway;

    public Response execute(String conversationId, String chatType) {
        try {
            ChatTypeEnum chatTypeEnum = ChatTypeEnum.getByType(chatType);
            if (!Objects.equals(chatTypeEnum, ChatTypeEnum.COMMON)) {
                return Response.buildSuccess();
            }
            ConversationEntity entity = conversationGateway.getByConversationId(conversationId);
            if (Objects.isNull(entity)) {
                return Response.buildSuccess();
            }
            if (StringUtils.isBlank(entity.getFirstAnswerContent())) {
                ChatMessageEntity chatMessageEntity = chatMessageGateway.getFirstAnswer(conversationId);
                if (Objects.nonNull(chatMessageEntity)) {
                    ConversationEntity conversationEntity = new ConversationEntity();
                    conversationEntity.setId(entity.getId());
                    conversationEntity.setFirstAnswerContent(StringUtils.substring(chatMessageEntity.getContent(), 0, 100));
                    conversationEntity.update();
                }
            }
        } catch (Exception e) {
            log.error("首次回答内容记录到会话表异常", e);
        }
        return Response.buildSuccess();
    }
}
