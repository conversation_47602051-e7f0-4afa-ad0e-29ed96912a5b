import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.dangbei.aisearch.common.enums.IntentEnum;
import lombok.SneakyThrows;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 单测
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-16
 */
public class JunitTest {

    Generation gen = new Generation();
    public static String LOCAL_INTENT_SYSTEM = """
        You are Qwen, created by Alibaba Cloud. You are a helpful assistant.
        You should choose one tag from the tag list:
        {}
        just reply with the chosen tag""";

    @Test
    @SneakyThrows
    public void intentTest() {
        String system = StrUtil.format(LOCAL_INTENT_SYSTEM, JSONUtil.toJsonPrettyStr(IntentEnum.buildPromptJson()));
        Message systemMsg = Message.builder()
            .role(Role.SYSTEM.getValue())
            .content(system)
            .build();
        System.out.println(system);

        List<Message> msgList = new ArrayList<>();
        msgList.add(systemMsg);
        msgList.add(Message.builder().role(Role.USER.getValue()).content("""
            https://p3-bot-sign.byteimg.com/tos-cn-i-v4nquku3lp/aef25443a6904199ab0fc60476b6cc4f.pdf~tplv-v4nquku3lp-image.image?rk3s=68e6b6b5&x-expires=1739622966&x-signature=z8PLRguiq5jRjvPRQvegFvaihQc%3D
            iphone16怎么样
            """).build());
//        msgList.add(Message.builder().role(Role.USER.getValue()).content("").build());
//        msgList.add(Message.builder().role(Role.USER.getValue()).content("iphone16怎么样").build());
//        msgList.add(Message.builder().role(Role.USER.getValue()).content("画一个老虎").build());
//        msgList.add(Message.builder().role(Role.USER.getValue()).content("早上好").build());

        GenerationParam param = GenerationParam.builder()
            .apiKey("sk-13f430c0f8574ba687211995cb64c297")
            .model("tongyi-intent-detect-v3")
            .messages(msgList)
            .resultFormat(GenerationParam.ResultFormat.MESSAGE)
            .build();

        String content = gen.call(param).getOutput().getChoices().get(0).getMessage().getContent();
        System.out.println();
        System.out.println(content);
    }

}
