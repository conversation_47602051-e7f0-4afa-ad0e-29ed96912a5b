package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.WriteEntity;

import java.util.List;

/**
 * Write 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
public interface WriteGateway extends BaseGateway<Long, WriteEntity> {

    /**
     * 查询所有的code
     * @return {@link List }<{@link String }>
     */
    List<String> listAllCode();
}
