package com.dangbei.aisearch.domain.gateway;

import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;

import java.util.List;

/**
 * KnowledgeApply 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
public interface KnowledgeApplyGateway extends BaseGateway<Long, KnowledgeApplyEntity> {

    /**
     * 查询用户对知识库的待处理申请
     * @param knowledgeId 知识库ID
     * @param applicantId 申请人ID
     * @return 申请实体，如果不存在则返回null
     */
    KnowledgeApplyEntity getPendingApply(String knowledgeId, String applicantId);

    /**
     * 根据申请ID查询申请记录
     * @param applyId 申请ID
     * @return 申请实体，如果不存在则返回null
     */
    KnowledgeApplyEntity getByApplyId(String applyId);

    /**
     * 查询用户对特定知识库的所有申请记录
     * @param knowledgeId 知识库ID
     * @param applicantId 申请人ID
     * @return 申请列表
     */
    List<KnowledgeApplyEntity> listUserAppliesForKnowledge(String knowledgeId, String applicantId);
}
