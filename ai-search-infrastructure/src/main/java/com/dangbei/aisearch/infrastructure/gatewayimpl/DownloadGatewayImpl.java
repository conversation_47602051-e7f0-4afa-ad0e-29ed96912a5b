package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.dangbei.aisearch.domain.entity.DownloadEntity;
import com.dangbei.aisearch.domain.gateway.DownloadGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.DownloadConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.DownloadDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.DownloadMapper;
import org.springframework.stereotype.Component;

/**
 * Download 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-27
 */
@Component
public class DownloadGatewayImpl extends BaseGatewayImpl<Long, DownloadEntity, DownloadDO, DownloadMapper, DownloadConvertor> implements DownloadGateway {

}
