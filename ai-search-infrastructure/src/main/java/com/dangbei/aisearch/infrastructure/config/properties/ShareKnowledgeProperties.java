package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-06-04 14:51
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "share-knowledge", autoRefreshed = true)
public class ShareKnowledgeProperties {

    /**
     * 默认共享知识库图标
     */
    private String defaultShareKnowledgeCoverUrl = "https://ai-search-static.dangbei.net/public/icon/9CAF96A9-7814-4F75-9E8E-9AFDEE0BD7A9.png";

    /**
     * 是否开启文档安全检测
     */
    private Boolean enableDocSecurityCheck = true;

    /**
     * 文档安全检测严格模式
     */
    private Boolean docSecurityCheckStrictMode = true;

    /**
     * 存储方式
     */
    private String storageType = "tos";

    /**
     * 火山知识库 namespace
     */
    private String namespace = "default";

    /**
     * 知识库名称
     */
    private String collectionName = "TEST";

    /**
     * 轮训文件状态超时时间,秒
     */
    private Integer maxPollingSeconds = 300;

    /**
     * 轮训文件状态间隔时间,毫秒
     */
    private Long pollingIntervalMillisecond = 5000L;

}
