package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.enums.RiskBizTypeEnum;
import com.dangbei.aisearch.client.enums.RiskTypeEnum;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.domain.entity.RiskEntity;
import com.dangbei.aisearch.domain.gateway.RiskGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.config.properties.RiskProperties;
import com.dangbei.aisearch.infrastructure.convertor.RiskConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.RiskDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.RiskMapper;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Risk 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-19
 */
@Component
public class RiskGatewayImpl extends BaseGatewayImpl<Long, RiskEntity, RiskDO, RiskMapper, RiskConvertor> implements RiskGateway {

    @Resource
    private RiskProperties riskProperties;

    @Override
    public boolean isIpRisk(String ip) {
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LambdaQueryWrapper<RiskDO> queryWrapper = Wrappers.lambdaQuery(RiskDO.class)
            .in(RiskDO::getRiskType, RiskTypeEnum.IP_FREQUENT_SWITCH_DEVICE.getType(), RiskTypeEnum.RISK_IP_DEVICE_CHANGE_IP.getType())
            .in(RiskDO::getRiskBizType, RiskBizTypeEnum.IP.getType())
            .eq(RiskDO::getRiskBizValue, ip)
            .ge(RiskDO::getCreateTime, yesterday)
            .last(LIMIT_ONE);
        return Objects.nonNull(baseMapper.selectOne(queryWrapper));
    }

    @Override
    public boolean inWhiteList(String userIdDefaultDeviceId) {
        String key = String.format(CacheKey.RISK_WHITE_LIST, userIdDefaultDeviceId);
        return RedisUtil.hasKey(key);
    }

    @Override
    public boolean addWhiteList(String userIdDefaultDeviceId) {
        String key = String.format(CacheKey.RISK_WHITE_LIST, userIdDefaultDeviceId);
        return RedisUtil.set(key, userIdDefaultDeviceId, riskProperties.getWhiteListSeconds(), TimeUnit.SECONDS);
    }
}
