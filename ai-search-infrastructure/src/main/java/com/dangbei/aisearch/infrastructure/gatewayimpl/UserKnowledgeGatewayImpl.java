package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.UserKnowledgeEntity;
import com.dangbei.aisearch.domain.gateway.ExternalCommonGateway;
import com.dangbei.aisearch.domain.gateway.UserKnowledgeGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.config.properties.KnowledgeDocProperties;
import com.dangbei.aisearch.infrastructure.convertor.UserKnowledgeConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.UserKnowledgeDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.UserKnowledgeMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * UserKnowledge 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
@Component
public class UserKnowledgeGatewayImpl extends BaseGatewayImpl<Long, UserKnowledgeEntity, UserKnowledgeDO, UserKnowledgeMapper, UserKnowledgeConvertor> implements UserKnowledgeGateway {

    @Resource
    private ExternalCommonGateway externalCommonGateway;
    @Resource
    private UserKnowledgeMapper userKnowledgeMapper;
    @Resource
    private UserKnowledgeConvertor userKnowledgeConvertor;
    @Resource
    private KnowledgeDocProperties knowledgeDocProperties;

    @Override
    public UserKnowledgeEntity getUserKnowledge(String userId) {
        LambdaQueryWrapper<UserKnowledgeDO> wrapper = Wrappers.lambdaQuery(UserKnowledgeDO.class);
        wrapper.eq(UserKnowledgeDO::getUserId, userId);
        wrapper.last(LIMIT_ONE);
        return userKnowledgeConvertor.toEntity(userKnowledgeMapper.selectOne(wrapper));
    }

    @Override
    @Lock4j(keys = "#userId", expire = 3000L)
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public UserKnowledgeEntity getOrCreateKnowledge(String userId, String deviceId) {
        // 查询用户知识库
        LambdaQueryWrapper<UserKnowledgeDO> wrapper = Wrappers.lambdaQuery(UserKnowledgeDO.class);
        wrapper.eq(UserKnowledgeDO::getUserId, userId);
        wrapper.last(LIMIT_ONE);
        UserKnowledgeDO userKnowledge = userKnowledgeMapper.selectOne(wrapper);
        if (Objects.nonNull(userKnowledge)) {
            return userKnowledgeConvertor.toEntity(userKnowledge);
        }

        UserKnowledgeEntity newAdd = new UserKnowledgeEntity();
        newAdd.setNamespace(knowledgeDocProperties.getNamespace());
        newAdd.setKnowledgeIdExt(knowledgeDocProperties.getCollectionName());

        newAdd.setKnowledgeId(externalCommonGateway.getDistributedId());
        newAdd.setUserId(userId);
        newAdd.setDeviceId(deviceId);
        newAdd.save();
        return newAdd;
    }

    @Override
    public UserKnowledgeEntity getByKnowledgeId(String knowledgeId) {
        LambdaQueryWrapper<UserKnowledgeDO> wrapper = Wrappers.lambdaQuery(UserKnowledgeDO.class);
        wrapper.eq(UserKnowledgeDO::getKnowledgeId, knowledgeId);
        return userKnowledgeConvertor.toEntity(userKnowledgeMapper.selectOne(wrapper));
    }

}
