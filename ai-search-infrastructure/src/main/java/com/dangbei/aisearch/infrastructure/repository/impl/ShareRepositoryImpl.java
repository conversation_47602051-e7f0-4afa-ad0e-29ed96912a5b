package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ShareDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ShareMapper;
import com.dangbei.aisearch.infrastructure.repository.ShareRepository;
import org.springframework.stereotype.Repository;

/**
 * Share 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-06
 */
@Repository
public class ShareRepositoryImpl extends ServiceImpl<ShareMapper, ShareDO> implements ShareRepository {

}
