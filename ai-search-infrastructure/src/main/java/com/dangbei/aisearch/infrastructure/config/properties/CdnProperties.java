package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 腾讯云/阿里云 cdn 配置
 * 腾讯云：<a href="https://cloud.tencent.com/document/product/1552/109329">...</a>
 * 阿里云：<a href="https://help.aliyun.com/zh/cdn/user-guide/configure-url-signing?spm=a2c4g.11186623.help-menu-27099.d_2_0_8_2_0.ba8b576bVovhAP">...</a>
 *
 * <AUTHOR>
 * @date 2025-04-25 10:08
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "cdn",
    autoRefreshed = true)
public class CdnProperties {

    /**
     * 腾讯 cdn 鉴权密钥
     */
    private String signatureSecretKey = "N8709fz1a7GQjgUnuXtacN7";

    /**
     * 用户 ID，暂未使用，默认为 0
     */
    private Integer uid = 0;

    /**
     * 鉴权加密串参数名称
     */
    private String signatureParamKey = "auth_key";
}
