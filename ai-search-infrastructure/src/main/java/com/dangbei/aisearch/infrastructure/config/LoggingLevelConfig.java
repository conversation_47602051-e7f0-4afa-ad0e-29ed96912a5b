package com.dangbei.aisearch.infrastructure.config;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.boot.nacos.config.properties.NacosConfigProperties;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.spring.beans.factory.annotation.ConfigServiceBeanBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.config.YamlMapFactoryBean;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ByteArrayResource;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 日志级别动态刷新
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-02-15
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class LoggingLevelConfig {

    @Resource
    private LoggingSystem loggingSystem;

    @Bean
    public ApplicationListener<ApplicationReadyEvent> loggingLevelNotify(ObjectProvider<ConfigServiceBeanBuilder> builder,
                                                                         NacosConfigProperties nacosConfigProperties,
                                                                         Environment env) {
        return event -> {
            // 新增项目启动初始化日志等级，springboot 版本读取 nacos 配置比 springboot 配置 LoggingSystem 要晚所以要手动设置
            Binder.get(env).bind("logging.level", Bindable.mapOf(String.class, LogLevel.class))
                .ifBound(loggingLevels -> loggingLevels.forEach(loggingSystem::setLogLevel));

            Map<String, Object> configMap = BeanUtil.beanToMap(nacosConfigProperties);
            ConfigService configService = builder.getIfAvailable().build(configMap);
            try {
                configService.addListener(nacosConfigProperties.getDataId(), nacosConfigProperties.getGroup(), new Listener() {
                    @Override
                    public Executor getExecutor() {
                        return Executors.newSingleThreadExecutor();
                    }

                    @Override
                    public void receiveConfigInfo(String content) {
                        YamlMapFactoryBean factoryBean = new YamlMapFactoryBean();
                        factoryBean.setResources(new ByteArrayResource(content.getBytes(StandardCharsets.UTF_8)));
                        Map<String, Object> config = factoryBean.getObject();
                        if (config == null) {
                            log.warn("[监听Nacos Config ] Content 为空");
                            return;
                        }
                        Object loggingMap = config.get("logging");
                        if (loggingMap instanceof Map<?, ?> loggingConfigs) {
                            Object levels = loggingConfigs.get("level");
                            if (levels instanceof Map<?, ?> levelConfigs) {
                                levelConfigs.forEach((packageInfo, level) ->
                                    loggingSystem.setLogLevel(String.valueOf(packageInfo), LogLevel.valueOf(String.valueOf(level).toUpperCase())));
                            }
                        }
                    }
                });
            } catch (NacosException e) {
                log.error("[添加Nacos Config监听器失败]", e);
            }
        };
    }

}
