package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.cola.dto.DTO;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 火山方舟大模型配置
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-02
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "volc-engine-ark",
    autoRefreshed = true)
public class VolcEngineArkProperties {

    /**
     * API Key
     */
    private String apiKey = "14703774-8424-4a0a-8936-6bc6f932fbd4";

    /**
     * 最大连接数
     */
    private Integer maxIdleConnections = 30;

    /**
     * 超时时间
     */
    private Integer keepAliveMinute = 10;

    /**
     * 语音服务配置
     */
    private VoiceConfig voiceConfig = new VoiceConfig();

    /**
     * 内置OkHttp配置
     */
    private InnerOkHttpConfig innerOkHttpConfig = new InnerOkHttpConfig();

    /**
     * 推荐问题推理点
     */
    private String suggestQuestModel = "ep-20250116152611-x7hgc";

    /**
     * 重写问题推理点
     */
    private String rewriteQuestionModel = "ep-20250117175217-pssc5";

    /**
     * DeepSeek-R1推理点
     */
    private String deepSeekR1Model = "ep-20250205094640-6pv5d";

    /**
     * DeepSeek-R1-32B蒸馏版推理点
     */
    private String deepSeekR1DistillModel = "ep-20250207141251-hsqnn";

    /**
     * DeepSeek-V3推理点
     */
    private String deepSeekV3Model = "ep-20250207150114-lzd9k";

    /**
     * 豆包主力模型
     */
    private String doubaoPro32K = "ep-20250210021055-zmvbw";

    /**
     * 智谱清言
     */
    private String glm3130b = "ep-20250314130504-vk5sz";

    /**
     * 月之暗面
     */
    private String moonshotV132k = "ep-20250314130331-vw77w";

    /**
     * 内置OkHttp配置
     */
    @Data
    public static class InnerOkHttpConfig extends DTO {

        /**
         * 调度器配置
         */
        private DispatcherConfig dispatcher = new DispatcherConfig();

        /**
         * http连接池配置
         */
        private ConnectionPoolConfig connectionPool = new ConnectionPoolConfig();

    }

    /**
     * 调度器配置
     */
    @Data
    public static class DispatcherConfig extends DTO {
        /**
         * 最大并发请求总数
         */
        private int maxRequests = 200;

        /**
         * 每个主机的最大并发请求数
         */
        private int maxRequestsPerHost = 200;

        /**
         * 核心线程数
         */
        private int corePoolSize = 100;

        /**
         * 最大线程数
         */
        private int maximumPoolSize = 300;

        /**
         * 空闲线程存活时间
         */
        private long keepAliveTime = 600L;

        /**
         * 阻塞队列长度
         */
        private int workQueueSize = 0;
    }

    /**
     * http连接池配置
     */
    @Data
    public static class ConnectionPoolConfig extends DTO {

        /**
         * 最大连接数
         */
        private Integer maxIdleConnections = 30;

        /**
         * 超时时间
         */
        private Integer keepAliveMinute = 10;

    }

    @Data
    public static class VoiceConfig extends DTO {

        /**
         * 应用ID
         */
        private String appId = "9312278890";

        /**
         * token
         */
        private String accessToken = "jsHNUTr9YcvS1AWqZusSdqoWwW8RFeyM";

    }

}
