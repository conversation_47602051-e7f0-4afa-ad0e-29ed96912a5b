package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 文档安全检测配置
 * <AUTHOR>
 * @date 2025-06-05 17:30
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "doc-security",
    autoRefreshed = true)
public class DocSecurityProperties {
    // 最大重试次数
    private Integer maxRetryCount = 100;
    // 最大轮询间隔(毫秒)
    private Long maxPollIntervalMillis = 3000L;
}
