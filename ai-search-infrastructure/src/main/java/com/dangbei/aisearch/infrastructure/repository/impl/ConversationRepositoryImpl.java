package com.dangbei.aisearch.infrastructure.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.ConversationDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.ConversationMapper;
import com.dangbei.aisearch.infrastructure.repository.ConversationRepository;
import org.springframework.stereotype.Repository;

/**
 * Conversation 仓库实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2024-12-25
 */
@Repository
public class ConversationRepositoryImpl extends ServiceImpl<ConversationMapper, ConversationDO> implements ConversationRepository {

}
