package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.dangbei.aisearch.client.dto.ChatMsgExt;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ChatMessage DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ais_chat_message", autoResultMap = true)
public class ChatMessageDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "会话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    @Schema(description = "智能体编码")
    @TableField(value = "bot_code")
    private String botCode;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "设备ID")
    @TableField(value = "device_id")
    private String deviceId;

    @Schema(description = "是否流式(1:是;0:否)")
    @TableField(value = "stream")
    private Integer stream;

    @Schema(description = "用户提问")
    @TableField(value = "question")
    private String question;

    @Schema(description = "文件列表")
    @TableField(value = "files")
    private String files;

    @Schema(description = "知识库列表")
    @TableField(value = "knowledge_list")
    private String knowledgeList;

    @Schema(description = "附加消息")
    @TableField(value = "meta_data")
    private String metaData;

    @Schema(description = "消息ID")
    @TableField(value = "msg_id")
    private String msgId;

    @Schema(description = "角色(user:用户;assistant:智能体)")
    @TableField(value = "role")
    private String role;

    @Schema(description = "消息类型")
    @TableField(value = "type")
    private String type;

    @Schema(description = "消息内容")
    @TableField(value = "content")
    private String content;

    @Schema(description = "消息内容类型")
    @TableField(value = "content_type")
    private String contentType;

    @Schema(description = "Chat ID")
    @TableField(value = "chat_id")
    private String chatId;

    @Schema(description = "创建时间")
    @TableField(value = "created_at")
    private Long createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at")
    private Long updatedAt;

    @Schema(description = "扩展字段")
    @TableField(value = "ext", typeHandler = Fastjson2TypeHandler.class)
    private ChatMsgExt ext;

    @Schema(description = "大模型供应商：dashScope-百炼 volcengine-火山引擎 tencentCloud-腾讯云")
    @TableField(value = "llm_provider")
    private String llmProvider;

    @Schema(description = "大语言模型名称")
    @TableField(value = "model")
    private String model;

    @Schema(description = "总token消耗")
    @TableField(value = "total_tokens")
    private Long totalTokens;

    @Schema(description = "输入token消耗")
    @TableField(value = "input_tokens")
    private Long inputTokens;

    @Schema(description = "输出token消耗")
    @TableField(value = "output_tokens")
    private Long outputTokens;

    @Schema(description = "模型回答耗时：单位秒")
    @TableField(value = "cost_time")
    private Integer costTime;

    @Schema(description = "首token回答耗时：单位毫秒")
    @TableField(value = "first_token_cost_ms")
    private Integer firstTokenCostMs;

    @Schema(description = "推荐状态(1:推荐;0:不推荐)")
    @TableField(value = "recommend_status")
    private Integer recommendStatus;

    @Schema(description = "应用类型(1:安卓,2:ios,3:harmony,4:mac,5:win,6:web,7:h5)")
    @TableField(value = "app_type")
    private Integer appType;

    @Schema(description = "标签")
    @TableField(value = "tag")
    private String tag;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
