package com.dangbei.aisearch.infrastructure.factory;

import com.dangbei.aisearch.domain.entity.DocumentResource;
import com.dangbei.aisearch.domain.gateway.DocumentResourceContentReader;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 文档资源读取器工厂
 * <AUTHOR>
 * @date 2025-06-01
 **/
@Component
public class DocumentResourceReaderFactory {

    @Resource
    private List<DocumentResourceContentReader> contentReaders;

    /**
     * 获取支持该文档资源的读取器
     * @param resource 文档资源
     * @return 内容读取器，如果没有支持的读取器，返回空
     */
    public Optional<DocumentResourceContentReader> getReader(DocumentResource resource) {
        if (contentReaders == null || contentReaders.isEmpty()) {
            return Optional.empty();
        }
        
        return contentReaders.stream()
                .filter(reader -> reader.support(resource))
                .findFirst();
    }
} 