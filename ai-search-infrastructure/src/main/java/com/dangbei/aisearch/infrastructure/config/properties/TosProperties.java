package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-04-15 17:32
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "tos",
    autoRefreshed = true)
public class TosProperties {

    /**
     * endpoint
     */
    private String endpoint;

    /**
     * 地域
     */
    private String regionId;

    /**
     * 域名
     */
    private String domainName;

    /**
     * 访问密钥
     */
    private String accessKeyId;

    /**
     * 访问密钥
     */
    private String accessKeySecret;

    /**
     * 角色ARN
     */
    private String roleTrn;

    /**
     * 自定义角色会话名称
     */
    private String roleSessionName;

    /**
     * 目标存储空间
     */
    private String bucket;

    /**
     * 访问凭证的有效时间 秒  范围15分钟～1个小时
     */
    private Integer durationSeconds;

    /**
     * 缓存时间
     */
    private Long cacheSeconds;

    /**
     * 秒
     * 预签名 url 过期时间
     * 默认 1 小时
     * 2592000
     */
    private Long preSignExpiration = 3600L;
}
