package com.dangbei.aisearch.infrastructure.convertor;

import com.dangbei.aisearch.domain.entity.RecommendAgentEntity;
import com.dangbei.aisearch.infrastructure.common.base.BaseConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.RecommendAgentDO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 转换器 Entity <---> DO
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-13
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface RecommendAgentConvertor extends BaseConvertor<RecommendAgentDO, RecommendAgentEntity> {

}
