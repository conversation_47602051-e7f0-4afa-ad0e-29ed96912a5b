package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UserKnowledgeDocMigrate DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_user_knowledge_doc_migrate")
public class UserKnowledgeDocMigrateDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    @Schema(description = "当贝知识库ID")
    @TableField(value = "knowledge_id")
    private String knowledgeId;

    @Schema(description = "当贝文档ID")
    @TableField(value = "doc_id")
    private String docId;

    @Schema(description = "迁移前外部知识库ID")
    @TableField(value = "old_knowledge_id_ext")
    private String oldKnowledgeIdExt;

    @Schema(description = "迁移后外部知识库ID")
    @TableField(value = "new_knowledge_id_ext")
    private String newKnowledgeIdExt;

    @Schema(description = "迁移前外部文档ID")
    @TableField(value = "old_doc_id_ext")
    private String oldDocIdExt;

    @Schema(description = "迁移后外部文档ID")
    @TableField(value = "new_doc_id_ext")
    private String newDocIdExt;

    @Schema(description = "用户文档名称")
    @TableField(value = "doc_name")
    private String docName;

    @Schema(description = "OSS文件地址")
    @TableField(value = "doc_path")
    private String docPath;

    @Schema(description = "文档的类型，如docx")
    @TableField(value = "doc_type")
    private String docType;

    @Schema(description = "文件字节数")
    @TableField(value = "doc_size")
    private Long docSize;

    @Schema(description = "文档的处理状态 0-处理中 1-处理完成 2-处理失败")
    @TableField(value = "process_status")
    private Integer processStatus;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
