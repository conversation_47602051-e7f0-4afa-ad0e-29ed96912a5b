package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ConversationAttachment DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_conversation_attachment")
public class ConversationAttachmentDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "会话ID")
    @TableField(value = "conversation_id")
    private String conversationId;

    @Schema(description = "附件文档ID")
    @TableField(value = "doc_id")
    private String docId;

    @Schema(description = "外部文档ID")
    @TableField(value = "doc_id_ext")
    private String docIdExt;

    @Schema(description = "用户文档名称")
    @TableField(value = "doc_name")
    private String docName;

    @Schema(description = "文件地址")
    @TableField(value = "doc_path")
    private String docPath;

    @Schema(description = "文档的类型，如docx")
    @TableField(value = "doc_type")
    private String docType;

    @Schema(description = "文件字节数")
    @TableField(value = "doc_size")
    private Integer docSize;

    @Schema(description = "文件MD5")
    @TableField(value = "md5")
    private String md5;

    @Schema(description = "文件内容总结")
    @TableField(value = "summary")
    private String summary;

    @Schema(description = "文档的处理状态 0-处理中 1-处理完成 2-处理失败")
    @TableField(value = "process_status")
    private Integer processStatus;

    @Schema(description = "失败原因")
    @TableField(value = "fail_reason")
    private String failReason;

    @Schema(description = "文档存储类型，oss/tos")
    @TableField(value = "storage_type")
    private String storageType;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
