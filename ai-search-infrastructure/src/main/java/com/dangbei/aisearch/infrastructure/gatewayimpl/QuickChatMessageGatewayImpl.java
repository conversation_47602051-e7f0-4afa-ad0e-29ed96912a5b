package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.domain.entity.QuickChatMessageEntity;
import com.dangbei.aisearch.domain.gateway.QuickChatMessageGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.QuickChatMessageConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.QuickChatMessageDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.QuickChatMessageMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * QuickChatMessage 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-04-01
 */
@Component
public class QuickChatMessageGatewayImpl extends BaseGatewayImpl<Long, QuickChatMessageEntity, QuickChatMessageDO, QuickChatMessageMapper, QuickChatMessageConvertor> implements QuickChatMessageGateway {

    @Override
    public List<QuickChatMessageEntity> listByConversationId(String conversationId) {
        LambdaQueryWrapper<QuickChatMessageDO> queryWrapper = Wrappers.lambdaQuery(QuickChatMessageDO.class).eq(QuickChatMessageDO::getConversationId, conversationId);
        return convertor.toEntityList(baseMapper.selectList(queryWrapper));
    }
}
