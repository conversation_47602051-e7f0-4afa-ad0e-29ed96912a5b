package com.dangbei.aisearch.infrastructure.knowledge.dto.co;

import com.alibaba.cola.dto.DTO;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-18 21:17
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class VolcKnowledgeInfoCo extends DTO {

    @JSONField(name = "collection_name")
    private String collectionName;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "create_time")
    private Long createTime;

    @JSONField(name = "update_time")
    private Long updateTime;

    @JSONField(name = "creator")
    private String creator;

    @JSONField(name = "pipeline_list")
    private List<Pipeline> pipelineList;

    @Data
    private static class Pipeline {
        @JSONField(name = "pipeline_type")
        private String pipelineType;
        @JSONField(name = "pipeline_stat")
        private PipelineStat pipelineStat;
    }

    @Data
    public static class PipelineStat {
        @JSONField(name = "doc_num")
        private Integer docNum;
        @JSONField(name = "finish_doc_num")
        private Integer finishDocNum;
        @JSONField(name = "point_num")
        private Integer pointNum;
        @JSONField(name = "success_doc_num")
        private Integer successDocNum;
    }

    /**
     * 获取知识库总文档数量
     * @return 总文档数量
     */
    public Integer getDocNum() {
        if (CollectionUtils.isNotEmpty(pipelineList)) {
            var pipeline = pipelineList.get(0);
            return Optional.ofNullable(pipeline).map(p -> p.pipelineStat).map(stat -> stat.docNum).orElse(0);
        }
        return 0;
    }

    /**
     * 获取知识库总切片数量
     * @return 总文档数量
     */
    public Integer getPointNum() {
        if (CollectionUtils.isNotEmpty(pipelineList)) {
            var pipeline = pipelineList.get(0);
            return Optional.ofNullable(pipeline).map(p -> p.pipelineStat).map(stat -> stat.pointNum).orElse(0);
        }
        return 0;
    }


}
