package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeMemberEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeMemberGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.KnowledgeMemberConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.KnowledgeMemberDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.KnowledgeMemberMapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * KnowledgeMember 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@Component
public class KnowledgeMemberGatewayImpl extends BaseGatewayImpl<Long, KnowledgeMemberEntity, KnowledgeMemberDO, KnowledgeMemberMapper, KnowledgeMemberConvertor> implements KnowledgeMemberGateway {

    @Override
    public KnowledgeMemberEntity getByKnowledgeIdAndUserId(String knowledgeId, String userId) {
        if (StringUtils.isAnyBlank(knowledgeId, userId)) {
            return null;
        }

        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeMemberDO::getKnowledgeId, knowledgeId);
        queryWrapper.eq(KnowledgeMemberDO::getUserId, userId);
        queryWrapper.last(LIMIT_ONE);

        KnowledgeMemberDO memberDO = baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(memberDO)) {
            return null;
        }
        return convertor.toEntity(memberDO);
    }

    @Override
    public int removeByKnowledgeId(String knowledgeId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return 0;
        }
        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeMemberDO::getKnowledgeId, knowledgeId);
        return baseMapper.delete(queryWrapper);
    }

    @Override
    public Long countByKnowledgeId(String knowledgeId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return 0L;
        }
        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeMemberDO::getKnowledgeId, knowledgeId);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Map<String, Long> batchCountByKnowledgeIds(List<String> knowledgeIds) {
        if (knowledgeIds == null || knowledgeIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, Long> resultMap = new HashMap<>(knowledgeIds.size());

        QueryWrapper<KnowledgeMemberDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("knowledge_id, count(*) as count")
            .in("knowledge_id", knowledgeIds)
            .groupBy("knowledge_id");

        List<Map<String, Object>> countResults = baseMapper.selectMaps(queryWrapper);

        // 将结果转换为Map
        for (Map<String, Object> result : countResults) {
            String knowledgeId = (String) result.get("knowledge_id");
            Long count = ((Number) result.get("count")).longValue();
            resultMap.put(knowledgeId, count);
        }

        // 对于没有成员的知识库，设置数量为0
        for (String knowledgeId : knowledgeIds) {
            resultMap.putIfAbsent(knowledgeId, 0L);
        }

        return resultMap;
    }

    @Override
    public Set<String> listCreatorAdminUserIdsExcludeCurrUserId(String knowledgeId, String userId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return Sets.newHashSet();
        }
        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = Wrappers.lambdaQuery(KnowledgeMemberDO.class)
            .select(KnowledgeMemberDO::getUserId)
            .eq(KnowledgeMemberDO::getKnowledgeId, knowledgeId)
            .in(KnowledgeMemberDO::getRole, SharedKnowledgeRoleEnum.CREATOR.getCode(), SharedKnowledgeRoleEnum.ADMIN.getCode());
        List<KnowledgeMemberDO> memberDOList = baseMapper.selectList(queryWrapper);
        return memberDOList.stream()
            .map(KnowledgeMemberDO::getUserId)
            .filter(memberUserId -> !Objects.equals(userId, memberUserId))
            .collect(Collectors.toSet());
    }

    @Override
    public Table<String, String, Integer> getKnowledgeIdUserIdRoleTable(Collection<String> knowledgeIds, Collection<String> userIds) {
        HashBasedTable<String, String, Integer> table = HashBasedTable.create();
        if (CollectionUtils.isEmpty(knowledgeIds) || CollectionUtils.isEmpty(userIds)) {
            return table;
        }

        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = Wrappers.lambdaQuery(KnowledgeMemberDO.class)
            .select(KnowledgeMemberDO::getKnowledgeId, KnowledgeMemberDO::getUserId, KnowledgeMemberDO::getRole)
            .in(KnowledgeMemberDO::getKnowledgeId, knowledgeIds)
            .in(KnowledgeMemberDO::getUserId, userIds);

        List<KnowledgeMemberDO> memberDOList = baseMapper.selectList(queryWrapper);

        // 填充Table
        for (KnowledgeMemberDO memberDO : memberDOList) {
            table.put(memberDO.getKnowledgeId(), memberDO.getUserId(), memberDO.getRole());
        }
        return table;
    }

    @Override
    public Set<String> listAdminMemberUserIdsExcludeCurrUserId(String knowledgeId, String userId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return Sets.newHashSet();
        }
        LambdaQueryWrapper<KnowledgeMemberDO> queryWrapper = Wrappers.lambdaQuery(KnowledgeMemberDO.class)
            .select(KnowledgeMemberDO::getUserId)
            .eq(KnowledgeMemberDO::getKnowledgeId, knowledgeId)
            .in(KnowledgeMemberDO::getRole, SharedKnowledgeRoleEnum.ADMIN.getCode(), SharedKnowledgeRoleEnum.MEMBER.getCode());
        return baseMapper.selectList(queryWrapper).stream()
            .map(KnowledgeMemberDO::getUserId)
            .filter(memberUserId -> !Objects.equals(userId, memberUserId))
            .collect(Collectors.toSet());
    }
}
