package com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dangbei.aisearch.infrastructure.common.base.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Download DO对象
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ais_download")
public class DownloadDO extends BaseDO<Long> {

    @Schema(description = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "平台 windows、mac-intel、mac-arm")
    @TableField(value = "platform")
    private String platform;

    @Schema(description = "来源")
    @TableField(value = "src")
    private String src;

    @Schema(description = "版本号")
    @TableField(value = "version")
    private String version;

    @Schema(description = "IP")
    @TableField(value = "ip")
    private String ip;

    @Override
    public Long getPrimaryId() {
        return this.id;
    }

    @Override
    public void setPrimaryId(Long id) {
        this.id = id;
    }
}
