package com.dangbei.aisearch.infrastructure.config.properties;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.dangbei.aisearch.infrastructure.config.properties.common.DocConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-22 14:48
 **/
@Data
@Component
@NacosConfigurationProperties(dataId = "${nacos.config.data-id:com.dangbei.ai-search.application.yaml}",
    groupId = "${nacos.config.group:ai-search}",
    prefix = "attachment", autoRefreshed = true)
public class AttachmentDocProperties {

    /**
     * 存储类型
     */
    private String storageType = "oss";

    /**
     * 附件配置
     * 文档类型：<a href="https://help.aliyun.com/zh/model-studio/long-context-qwen-long?spm=a2c4g.11186623.0.0.45d7505dHOoXYn">...</a>
     * 图片类型：<a href="https://www.volcengine.com/docs/82379/1362931#%E5%9B%BE%E7%89%87%E6%A0%BC%E5%BC%8F%E8%AF%B4%E6%98%8E">...</a>
     */
    private List<DocConfig> docConfig;


    /**
     * 文件类型的附件推荐问题
     */
    private List<String> fileSuggestQuestion;

    /**
     * 图片类型的附件推荐问题
     */
    private List<String> imageSuggestQuestion;


    /**
     * 文件名长度限制
     */
    private Integer attachmentFileNameLengthLimit = 128;
    
    /**
     * 直接读取的文件类型列表
     */
    private List<String> directReadTypes;
    
    /**
     * 直接读取的文件大小上限（字节）
     */
    private Integer directReadSizeLimit = 2 * 1024 * 1024; // 默认2MB
}
