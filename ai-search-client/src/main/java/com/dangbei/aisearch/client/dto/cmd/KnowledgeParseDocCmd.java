package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 知识库解析文件命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnowledgeParseDocCmd extends DTO {

    @NotBlank(message = "文件路径为空")
    @Schema(description = "OSS文件路径")
    private String filePath;

    @NotBlank(message = "文件名称不能为空")
    @Schema(description = "用户文件名称")
    private String fileName;

    @Schema(description = "文件大小")
    private String fileSize;

    @Schema(description = "文档存储方式，1: oss, 2, tos")
    @Min(value = 1, message = "storageType不合法")
    @Max(value = 2, message = "storageType不合法")
    private Integer storageType = 1;

}
