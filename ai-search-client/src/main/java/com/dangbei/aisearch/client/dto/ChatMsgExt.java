package com.dangbei.aisearch.client.dto;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.dto.clientobject.TokenUsageCo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 聊天记录扩展信息
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-15
 */
@Data
public class ChatMsgExt extends DTO {

    @Schema(description = "是否深度搜索")
    private Boolean isDeep = Boolean.FALSE;

    @Schema(description = "对话请求对象，格式为ChatCmd.class")
    private String chatCmd;

    @Schema(description = "提问的引用来源，格式为List<ReferenceItem>")
    private String references;

    @Schema(description = "搜索来源，格式为DbCard2WebSearchCo.class")
    private String searchReferences;

    @Schema(description = "推荐问题，格式为List<String>")
    private String followUp;

    @Schema(description = "模型思考内容")
    private String thinking;

    @Schema(description = "链路ID")
    private String requestId;

    @Schema(description = "外部链路ID")
    private String requestIdExt;

    @Schema(description = "请求失败信息")
    private String failMsg;

    @Schema(description = "Token使用量统计")
    private TokenUsageCo tokenUsageCo;

    @Schema(description = "回答是否支持下载")
    private Boolean supportDownload = Boolean.FALSE;

    @Schema(description = "上下文清除标记 true:清除上下文")
    private Boolean ctxClearFlag = Boolean.FALSE;

}
