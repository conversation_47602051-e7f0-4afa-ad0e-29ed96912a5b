package com.dangbei.aisearch.client.dto;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WriteOption extends DTO {

    @Schema(description = "展示名称")
    private String showName;

    @Schema(description = "值")
    private String value;

    @Schema(description = "是否默认")
    private boolean isDefault;
}
