package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScreenShotDownloadInfoCo extends ClientObject {

    @Schema(description = "windows平台文件信息")
    private WindowsInfo windowsInfo;

    @Schema(description = "mac平台文件信息")
    private MacInfo macInfo;

    @Data
    public static class WindowsInfo {

        @Schema(description = "下载地址")
        private String downloadUrl;
    }

    @Data
    public static class MacInfo {
        @Schema(description = "Intel芯片版本信息")
        private ChipVersion intelVersion;

        @Schema(description = "Apple芯片版本信息")
        private ChipVersion appleVersion;
    }

    @Data
    public static class ChipVersion {

        @Schema(description = "下载地址")
        private String downloadUrl;
    }
}

