package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.enums.SharedKnowledgeRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 成员权限客户端对象
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class MemberPermissionsCo extends DTO {

    @Schema(description = "是否是成员")
    private Boolean isMember;

    /**
     * {@link SharedKnowledgeRoleEnum}
     */
    @Schema(description = "角色：1-创建人，2-管理员，3-成员")
    private Integer role;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "权限列表")
    private List<String> permissions;
}
