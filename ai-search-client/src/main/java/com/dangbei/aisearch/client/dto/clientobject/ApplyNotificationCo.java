package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.ClientObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApplyNotificationCo extends ClientObject {

    @Schema(description = "申请ID")
    private String applyId;

    @Schema(description = "申请状态：0-待审批，1-已通过，2-已拒绝")
    private Integer applyStatus;

    @Schema(description = "申请人昵称")
    private String applicantNickname;

    @Schema(description = "申请人头像")
    private String applicantAvatar;

    @Schema(description = "申请人是否是自己")
    private boolean applicantIsSelf;

    @Schema(description = "审批人昵称")
    private String approverNickname;

    @Schema(description = "审批人头像")
    private String approverAvatar;

    @Schema(description = "审批人角色")
    private String approverRole;

    @Schema(description = "审批人是否是自己")
    private boolean approverIsSelf;
}
