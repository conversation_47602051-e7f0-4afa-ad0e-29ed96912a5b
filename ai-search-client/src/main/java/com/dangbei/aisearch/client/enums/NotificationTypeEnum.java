package com.dangbei.aisearch.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 通知类型枚举
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Getter
@AllArgsConstructor
public enum NotificationTypeEnum {

    TEXT(1, "普通文本"),

    APPROVAL(2, "审批");

    private final Integer code;
    private final String desc;

    public static NotificationTypeEnum getByCode(Integer code) {
        for (NotificationTypeEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

}
