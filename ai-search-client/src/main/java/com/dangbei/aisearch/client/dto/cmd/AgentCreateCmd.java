package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.DTO;
import com.dangbei.aisearch.client.dto.AgentChatExample;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 智能体创建命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentCreateCmd extends DTO {

    @Schema(description = "智能体Id")
    private String agentId;

    @Schema(description = "智能体角色类型：0-角色类 1-助理类")
    @NotNull(message = "智能体角色类型不能为空")
    @Max(value = 1, message = "智能体角色类型错误")
    @Min(value = 0, message = "智能体角色类型错误")
    private Integer agentRole;

    @Schema(description = "智能体")
    @NotBlank(message = "智能体头像不能为空")
    private String agentAvatar;

    @Schema(description = "APP端背景图")
    private String appBgUrl;

    @Schema(description = "智能体背景主色调")
    private String appBgColorTone;

    @Schema(description = "智能体名称")
    @NotBlank(message = "智能体名称不能为空")
    private String agentName;

    @Schema(description = "智能体性别：0-男 1-女 2-非人类角色")
    @Min(value = 0, message = "智能体性别错误")
    @Max(value = 2, message = "智能体性别错误")
    private Integer gender;

    @Schema(description = "角色/工具设定")
    @NotBlank(message = "角色/工具设定不能为空")
    private String userPrompt;

    @Schema(description = "智能体语音开关 0-关 1-开")
    @NotNull(message = "智能体语音开关设定不能为空")
    private Integer voiceEnabled;

    @Schema(description = "智能体音色：https://www.volcengine.com/docs/6561/1257544")
    private String voiceType;

    @Schema(description = "智能体公开状态：0-公开 1-私密 2-部分公开")
    @NotNull(message = "智能体公开状态不能为空")
    @Min(value = 0, message = "智能体公开状态错误")
    @Max(value = 2, message = "智能体公开状态错误")
    private Integer visibility;

    @Schema(description = "角色/工具简介")
    private String intro;

    @Schema(description = "智能体描述")
    private String description;

    @Schema(description = "智能体开场白")
    private String greeting;

    @Schema(description = "推荐提问")
    private List<String> followUp;

    @Schema(description = "角色性格描述")
    private String personality;

    @Schema(description = "智能体对话示例")
    private List<AgentChatExample> chatExample;

    @Schema(description = "智能体技能")
    private List<String> skills;
}
