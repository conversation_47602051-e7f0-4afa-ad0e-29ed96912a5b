package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 共享知识库文档信息客户端对象
 * <AUTHOR>
 * @date 2025-05-28
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SharedKnowledgeDocInfoCo extends DTO {

    @Schema(description = "文档ID")
    private String docId;

    @Schema(description = "OSS文件路径")
    private String filePath;

    /**
     * {@code DocProcessStatusEnum}
     */
    @Schema(description = "处理状态：0-处理中，1-处理完成，2-处理失败 3-内容不合法")
    private Integer processStatus;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "文档类型（如：pdf、doc、txt等）")
    private String docType;

    @Schema(description = "文档字数")
    private Long wordNum;

    @Schema(description = "文档大小（字节）")
    private Long docSize;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "文档摘要")
    private String summary;

    @Schema(description = "是否可以删除（当前用户是否有权限删除此文档）")
    private Boolean canDelete;

    @Schema(description = "是否可以重命名（当前用户是否有权限重命名此文档）")
    private Boolean canRename;

    @Schema(description = "是否可以下载（当前用户是否有权限下载此文档）")
    private Boolean canDownload;
}
