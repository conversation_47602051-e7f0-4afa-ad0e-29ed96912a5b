package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserInfoUpdateCmd extends Command {

    @Size(max = 20, message = "昵称不能超过20个字符")
    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;
    
}
