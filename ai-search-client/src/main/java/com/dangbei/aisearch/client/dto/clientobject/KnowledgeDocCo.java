package com.dangbei.aisearch.client.dto.clientobject;

import com.alibaba.cola.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库文档Co¬对象
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-10
 */
@Data
public class KnowledgeDocCo extends DTO {

    @Schema(description = "文档ID")
    private String docId;

    @Schema(description = "文档名称")
    private String docName;

    @Schema(description = "文档的类型，如docx")
    private String docType;

    @Schema(description = "文档字数")
    private Long wordNum;

    @Schema(description = "文档大小")
    private Long docSize;

    @Schema(description = "文档的处理状态 0-处理中 1-处理完成 2-处理失败，3-处理失败")
    private Integer processStatus;

    @Schema(description = "上传时间戳")
    private String createTime;

    @Schema(description = "文档摘要")
    private String summary;

}
