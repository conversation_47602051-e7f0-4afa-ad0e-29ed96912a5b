package com.dangbei.aisearch.client.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 消息取消点亮命令
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageLightDownCmd extends Command {

    @NotBlank(message = "消息ID不能为空")
    @Schema(description = "消息ID")
    private String msgId;

}
