-- 共享知识库
CREATE TABLE ais_shared_knowledge (
  id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  knowledge_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '当贝知识库ID',
  knowledge_id_ext VARCHAR(128) DEFAULT NULL COMMENT '外部知识库ID',
  namespace VARCHAR(64) NOT NULL DEFAULT 'default' COMMENT '命名空间',
  create_user_id VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建人ID',
  name VARCHAR(64) NOT NULL DEFAULT '' COMMENT '知识库名称',
  cover_url VARCHAR(256) DEFAULT NULL COMMENT '封面图片URL',
  description TEXT DEFAULT NULL COMMENT '知识库描述',
  member_count INT(11) NOT NULL DEFAULT '1' COMMENT '成员数量',
  join_approval_required TINYINT(4) NOT NULL DEFAULT '1' COMMENT '是否需要审批加入',
  download_enabled TINYINT(4) NOT NULL DEFAULT '0' COMMENT '成员是否可下载',
  data_type VARCHAR(64) NOT NULL DEFAULT 'unstructured_data' COMMENT '数据类型',
  create_person VARCHAR(16) DEFAULT '' COMMENT '创建人',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
  update_person VARCHAR(16) DEFAULT '' COMMENT '修改人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务更新时间',
  is_deleted BIGINT(20) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
  db_modify_timestamp TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_knowledge_id (knowledge_id),
  KEY idx_create_user_id (create_user_id)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='共享知识库表';

CREATE TABLE ais_shared_knowledge_doc (
  id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  knowledge_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '当贝知识库ID',
  doc_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '当贝文档ID',
  doc_name VARCHAR(256) DEFAULT NULL COMMENT '用户文档名称',
  doc_path VARCHAR(128) DEFAULT NULL COMMENT 'OSS文件地址',
  doc_type VARCHAR(16) DEFAULT NULL COMMENT '文档的类型，如docx',
  doc_size INT(11) NOT NULL DEFAULT '0' COMMENT '文件字节数',
  md5 VARCHAR(64) NOT NULL DEFAULT '' COMMENT '文件MD5',
  process_status INT(11) NOT NULL DEFAULT '0' COMMENT '文档的处理状态 0-处理中 1-处理完成 2-处理失败',
  point_num INT(11) NOT NULL DEFAULT '0' COMMENT '文档提取出的point数量',
  word_num BIGINT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '文档字数',
  doc_id_ext VARCHAR(128) NOT NULL DEFAULT '' COMMENT '外部文档ID',
  fail_reason VARCHAR(128) NOT NULL DEFAULT '' COMMENT '失败原因',
  volc_delete_flag TINYINT(4) NOT NULL DEFAULT '0' COMMENT '火山知识库文档删除标记 0-未删除 1-已删除 2-删除失败',
  volc_delete_fail_reason VARCHAR(128) NOT NULL DEFAULT '' COMMENT '火山知识库文档删除失败原因',
  summary LONGTEXT DEFAULT NULL COMMENT '文档内容总结',
  upload_user_id VARCHAR(64) NOT NULL DEFAULT '' COMMENT '上传用户ID',
  add_type VARCHAR(20) NOT NULL DEFAULT '' COMMENT '文档添加类型，tos/url',
  storage_type VARCHAR(20) NOT NULL DEFAULT 'oss' COMMENT '文档存储类型，oss/tos',
  create_person VARCHAR(16) DEFAULT '' COMMENT '创建人',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
  update_person VARCHAR(16) DEFAULT '' COMMENT '修改人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务更新时间',
  is_deleted BIGINT(20) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
  db_modify_timestamp TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_doc_id (doc_id),
  KEY idx_knowledge_id (knowledge_id),
  KEY `idx_doc_name` (`doc_name`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_doc_id_ext` (`doc_id_ext`)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='共享知识库文档表';

CREATE TABLE ais_knowledge_member (
  id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
  knowledge_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识库ID',
  user_id VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户ID',
  role TINYINT(4) NOT NULL DEFAULT '3' COMMENT '角色(1-创建人,2-管理员,3-成员)',
  join_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  invite_user_id VARCHAR(64) DEFAULT NULL COMMENT '邀请人ID',
  create_person VARCHAR(16) DEFAULT '' COMMENT '创建人',
  create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
  update_person VARCHAR(16) DEFAULT '' COMMENT '修改人',
  update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务更新时间',
  is_deleted BIGINT(20) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
  db_modify_timestamp TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
  PRIMARY KEY (id),
  KEY idx_user_id (user_id),
  KEY idx_role (role)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='知识库成员表';


CREATE TABLE ais_knowledge_apply (
     id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键id',
     apply_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '申请ID',
     knowledge_id VARCHAR(128) NOT NULL DEFAULT '' COMMENT '知识库ID',
     applicant_id VARCHAR(64) NOT NULL DEFAULT '' COMMENT '申请人ID',
     status TINYINT(4) NOT NULL DEFAULT '0' COMMENT '申请状态(0-申请中,1-同意,2-拒绝)',
     approver_id VARCHAR(64) DEFAULT NULL COMMENT '审批人ID',
     apply_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
     process_time DATETIME DEFAULT NULL COMMENT '处理时间',
     create_person VARCHAR(16) DEFAULT '' COMMENT '创建人',
     create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
     update_person VARCHAR(16) DEFAULT '' COMMENT '修改人',
     update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务更新时间',
     is_deleted BIGINT(20) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
     db_modify_timestamp TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
     PRIMARY KEY (id),
     UNIQUE KEY uk_apply_id (apply_id),
     KEY idx_knowledge_id (knowledge_id)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='知识库申请表';

CREATE TABLE `ais_user_notification`
(
    `id`                  bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `type`                int(11)             NOT NULL COMMENT '类型(1-普通文本,2-审批)',
    `biz_type`            int(11)             NOT NULL DEFAULT '1' COMMENT '业务类型(1-知识库加入申请,2-知识库申请结果)',
    `user_id`             varchar(64)         NOT NULL DEFAULT '' COMMENT '接收用户ID',
    `title`               varchar(128)                 DEFAULT '' COMMENT '标题',
    `content`             text                         DEFAULT NULL COMMENT '内容',
    `related_id`          varchar(128)                 DEFAULT NULL COMMENT '关联ID',
    `jump_config`         varchar(1024)                DEFAULT NULL COMMENT '跳转配置JSON',
    `is_read`             int(11)             NOT NULL DEFAULT '0' COMMENT '是否已读(0-未读,1-已读)',
    `read_time`           datetime                     DEFAULT NULL COMMENT '阅读时间',
    `create_person`       varchar(16)                  DEFAULT '' COMMENT '创建人',
    `create_time`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '业务创建时间',
    `update_person`       varchar(16)                  DEFAULT '' COMMENT '修改人',
    `update_time`         datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '业务更新时间',
    `is_deleted`          bigint(20)          NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0-未删除,其他-已删除',
    `db_modify_timestamp` timestamp(3)        NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库变更时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_unread` (`user_id`, `is_read`, `biz_type`),
    KEY `idx_biz_type` (`biz_type`)
) AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT = '用户通知表';

ALTER TABLE ais_user_knowledge_doc
    DROP INDEX uk_doc_path;

# 知识库分享
alter table ais_share add column `knowledge_id` varchar(128) DEFAULT NULL COMMENT '当贝知识库ID' AFTER `agent_id`;

alter table ais_chat_message add column `knowledge_list` JSON null comment '知识库列表';
