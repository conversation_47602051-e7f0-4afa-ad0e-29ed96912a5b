package com.dangbei.aisearch.adapter.web;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.dangbei.aisearch.app.service.ShareService;
import com.dangbei.aisearch.client.dto.BaseDTO;
import com.dangbei.aisearch.client.dto.clientobject.AgentShareCo;
import com.dangbei.aisearch.client.dto.clientobject.KnowledgeShareCo;
import com.dangbei.aisearch.client.dto.clientobject.ShareListCo;
import com.dangbei.aisearch.client.dto.clientobject.WxJsapiSignatureCo;
import com.dangbei.aisearch.client.dto.cmd.ShareAddOrUpdateCmd;
import com.dangbei.aisearch.client.dto.cmd.ShareLikeCmd;
import com.dangbei.aisearch.client.dto.cmd.ShareStopCmd;
import com.dangbei.aisearch.client.dto.cmd.ShareUnLikeCmd;
import com.dangbei.aisearch.client.dto.cmd.WxShareInfoCmd;
import com.dangbei.aisearch.client.dto.cmd.query.SharePageQuery;
import com.dangbei.aisearch.client.dto.cmd.query.ShareQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Share 服务接口
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-01-07
 */
@Tag(name = "ShareController", description = "ShareController服务")
@RequestMapping("/shareApi/v1")
@RestController
public class ShareController {

    @Resource
    private ShareService shareService;

    @PostMapping("/getShareId")
    @Operation(summary = "获取分享ID", description = "获取分享ID")
    public SingleResponse<String> getShareId(@RequestBody BaseDTO baseDTO) {
        return shareService.getShareId();
    }

    @PostMapping("/createOrUpdate")
    @Operation(summary = "创建分享", description = "创建分享")
    public SingleResponse<String> createOrUpdate(@Valid @RequestBody ShareAddOrUpdateCmd cmd) {
        return shareService.createOrUpdate(cmd);
    }

    @PostMapping("/stop")
    @Operation(summary = "停止分享", description = "停止分享")
    public Response stop(@Valid @RequestBody ShareStopCmd stopCmd) {
        return shareService.stop(stopCmd.getShareId());
    }

    @PostMapping("/pageQuery")
    @Operation(summary = "分页查询会话分享", description = "分页查询会话分享")
    public SingleResponse<ShareListCo> pageQuery(@Valid @RequestBody SharePageQuery pageQuery) {
        return shareService.pageQuery(pageQuery);
    }

    @PostMapping("/like")
    @Operation(summary = "点赞", description = "点赞")
    public Response like(@Valid @RequestBody ShareLikeCmd cmd) {
        return shareService.like(cmd.getShareId());
    }

    @PostMapping("/unlike")
    @Operation(summary = "取消点赞", description = "取消点赞")
    public Response unlike(@Valid @RequestBody ShareUnLikeCmd cmd) {
        return shareService.unlike(cmd.getShareId());
    }

    @PostMapping("/wxShareInfo")
    @Operation(summary = "获取微信分享链接", description = "获取微信分享链接")
    public SingleResponse<WxJsapiSignatureCo> wxShareInfo(@Valid @RequestBody WxShareInfoCmd cmd) {
        return shareService.wxShareInfo(cmd);
    }

    @PostMapping("/agent/info")
    @Operation(summary = "获取智能体分享信息", description = "获取智能体分享信息")
    public SingleResponse<AgentShareCo> agentInfo(@Valid @RequestBody ShareQuery query) {
        return shareService.agentInfo(query);
    }

    @PostMapping("/knowledge/info")
    @Operation(summary = "获取知识库分享信息", description = "获取知识库分享信息")
    public SingleResponse<KnowledgeShareCo> knowledgeInfo(@Valid @RequestBody ShareQuery query) {
        return shareService.knowledgeInfo(query);
    }
}
