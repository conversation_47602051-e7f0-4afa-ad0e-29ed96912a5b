package com.dangbei.aisearch.adapter.common.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.util.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.coze.openapi.client.chat.model.ChatEventType;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.dangbei.aisearch.app.assembler.ChatMessageAssembler;
import com.dangbei.aisearch.app.model.MessageModel;
import com.dangbei.aisearch.app.util.UserDeviceUtil;
import com.dangbei.aisearch.client.dto.UserDeviceDTO;
import com.dangbei.aisearch.client.dto.cmd.ChatCmd;
import com.dangbei.aisearch.common.constant.CacheKey;
import com.dangbei.aisearch.common.enums.MsgContentTypeEnum;
import com.dangbei.aisearch.common.util.ObjectMapperUtil;
import com.dangbei.aisearch.domain.entity.ChatMessageEntity;
import com.dangbei.aisearch.domain.gateway.ChatMessageGateway;
import com.dangbei.aisearch.infrastructure.config.properties.RateLimiterProperties;
import com.dangbei.framework.insight.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.dangbei.aisearch.common.util.TimeUtil.getTodayRemainSeconds;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-02-10
 */
@Slf4j
@Aspect
@Component
public class RateLimitAspect {

    @Resource
    private RateLimiterProperties rateLimiterProperties;
    @Resource
    private ChatMessageAssembler chatMessageAssembler;
    @Resource
    private ChatMessageGateway chatMessageGateway;

    @Around("execution(* com.dangbei.aisearch.adapter.web.ChatController.chat(com.dangbei.aisearch.client.dto.cmd.ChatCmd,javax.servlet.http.HttpServletRequest)) ||"
        + "execution(* com.dangbei.aisearch.adapter.web.ChatController.chatV2(com.dangbei.aisearch.client.dto.cmd.ChatCmd,javax.servlet.http.HttpServletRequest)) ||"
        + "execution(* com.dangbei.aisearch.adapter.web.app.AppChatController.chat(com.dangbei.aisearch.client.dto.cmd.ChatCmd,javax.servlet.http.HttpServletRequest)) ||"
        + "execution(* com.dangbei.aisearch.adapter.web.AgentController.agentChat(com.dangbei.aisearch.client.dto.cmd.AgentChatCmd,javax.servlet.http.HttpServletRequest))||"
        + " execution(* com.dangbei.aisearch.adapter.web.app.AppAgentController.agentChat(com.dangbei.aisearch.client.dto.cmd.AgentChatCmd,javax.servlet.http.HttpServletRequest))"
    )
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!rateLimiterProperties.isEnabled()) {
            return joinPoint.proceed();
        }
        HttpServletRequest request = getHttpServletRequest();
        if (Objects.isNull(request)) {
            return joinPoint.proceed();
        }

        UserDeviceDTO userDeviceDTO = UserDeviceUtil.getUserDeviceInfo();
        boolean isFrequent;
        if (StringUtils.isBlank(userDeviceDTO.getUserId())) {
            isFrequent = isFrequent(String.format(CacheKey.RATE_LIMIT, "deviceId", userDeviceDTO.getDeviceId()), rateLimiterProperties.getDeviceLimitTimes())
                || isFrequent(String.format(CacheKey.RATE_LIMIT, "ip", HttpUtil.getIp(request)), rateLimiterProperties.getIpLimitTimes());
        } else {
            isFrequent = isFrequent(String.format(CacheKey.RATE_LIMIT, "ip", HttpUtil.getIp(request)), rateLimiterProperties.getIpLimitTimes());
        }

        Object arg = joinPoint.getArgs()[0];
        ChatCmd chatCmd = getChatCmd(arg);

        // 封禁问题
        if (isBanQuestion(chatCmd.getQuestion())) {
            return null;
        }

        if (isFrequent) {
            return doOnLimited(chatCmd);
        } else {
            return joinPoint.proceed();
        }
    }

    private ChatCmd getChatCmd(Object arg) {
        ChatCmd chatCmd;
        if (arg instanceof ChatCmd) {
            chatCmd = (ChatCmd) arg;
        } else {
            chatCmd = JSON.parseObject(JSON.toJSONString(arg), ChatCmd.class);
            chatCmd.setBotCode("AGENT_CHAT");
        }
        return chatCmd;
    }

    private SseEmitter doOnLimited(ChatCmd chatCmd) throws IOException {
        // 发送限流信息
        String answerContent = rateLimiterProperties.getRateLimitMsg();
        MessageModel.Msg msg = buildMsg(chatCmd, answerContent);
        SseEmitter sseEmitter = sseEmitterSend(ObjectMapperUtil.toJson(msg));

        List<ChatMessageEntity> messageList = chatMessageAssembler.getQuestionAndAnswerMessageList(chatCmd, answerContent);
        for (ChatMessageEntity messageEntity : messageList) {
            Optional.ofNullable(messageEntity.getExt())
                .ifPresent(chatMsgExt -> chatMsgExt.setFailMsg(answerContent));
        }
        chatMessageGateway.insertBatch(messageList);
        return sseEmitter;
    }

    private static MessageModel.Msg buildMsg(ChatCmd chatCmd, String answerContent) {
        MessageModel.Msg msg = new MessageModel.Msg();
        msg.setConversationId(chatCmd.getConversationId());
        msg.setType(MessageType.ANSWER.getValue());
        msg.setContent(answerContent);
        msg.setContentType(MsgContentTypeEnum.TEXT.getValue());
        return msg;
    }

    private static SseEmitter sseEmitterSend(String data) throws IOException {
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        emitter.send(SseEmitter.event().name(ChatEventType.CONVERSATION_CHAT_FAILED.getValue()).data(data));
        emitter.complete();
        return emitter;
    }

    private boolean isFrequent(String key, Integer timesLimit) {
        return RedisUtil.isFrequent(key, timesLimit, getTodayRemainSeconds());
    }

    private HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(servletRequestAttributes)) {
            return servletRequestAttributes.getRequest();
        }
        return null;
    }

    private boolean isBanQuestion(String question) {
        return CollUtil.isNotEmpty(rateLimiterProperties.getBanQuestionList())
            && StrUtil.isNotBlank(question)
            && rateLimiterProperties.getBanQuestionList().stream()
            .filter(StrUtil::isNotBlank)
            .anyMatch(question::contains);
    }
}
