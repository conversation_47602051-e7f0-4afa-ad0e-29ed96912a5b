package com.dangbei.aisearch.adapter.web;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.common.util.HttpUtil;
import com.dangbei.aisearch.domain.entity.DownloadEntity;
import com.dangbei.aisearch.infrastructure.config.properties.DownloadProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 下载服务
 * <AUTHOR> href="mailto:<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-03-26
 */
@RestController
@RequestMapping("/download")
@Tag(name = "DownloadController", description = "下载服务")
public class DownloadController {

    @Resource
    private DownloadProperties downloadProperties;

    @GetMapping("/{platform}")
    @Operation(summary = "下载", description = "下载")
    public ResponseEntity<Void> download(@PathVariable String platform,
                                         @RequestParam(value = "version", required = false) String version,
                                         @RequestParam(value = "src", required = false) String src,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws IOException {
        // 平台校验
        boolean valid = Platform.isValid(platform);
        if (!valid) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        // 保存统计
        DownloadEntity entity = new DownloadEntity();
        entity.setPlatform(platform);
        entity.setSrc(StrUtil.subPre(src, 64));
        entity.setVersion(version);
        entity.setIp(HttpUtil.getIp(request));
        entity.save();

        // 获取真实下载链接
        String link = downloadProperties.getRealLink().get(platform);
        if (StrUtil.isBlank(link)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        // 重定向
        response.sendRedirect(link);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Getter
    @AllArgsConstructor
    public enum Platform {
        WINDOWS("windows"),
        MAC_INTEL("mac-intel"),
        MAC_ARM("mac-arm"),
        MAC("mac"),
        ANDROID("android");

        private final String platform;

        /**
         * 是否合法
         * @param platform 平台
         * @return true=合法
         */
        public static boolean isValid(String platform) {
            for (Platform p : Platform.values()) {
                if (p.getPlatform().equals(platform)) {
                    return true;
                }
            }
            return false;
        }
    }

}
